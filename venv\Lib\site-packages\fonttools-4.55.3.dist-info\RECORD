../../Scripts/fonttools.exe,sha256=1fO5QKa0E8HSPkwx5lWLUKZgIa9jATtNsExtBXg-1lA,108407
../../Scripts/pyftmerge.exe,sha256=ihCoj_4yxouectneUP6sttg4WIOVq5Y9eUzTy_QC4wA,108404
../../Scripts/pyftsubset.exe,sha256=cB24ASGjNFJ9K8siqTTNOHOYgISq4rv4TmmwnqNCTqM,108405
../../Scripts/ttx.exe,sha256=j8W9hdjb9Mn-8AxG51ka6Lv08xHcWxD2zRBojw0Fy1E,108402
../../share/man/man1/ttx.1,sha256=E71F9mRNWlttVpzlnP7w_fqkQygPkph5s-AtVa0Js50,5601
fontTools/__init__.py,sha256=MP_1iTN0EFf0VaKmXbVsbAsJ-QgvrRYdnaPmVQUc-JQ,191
fontTools/__main__.py,sha256=T8Tg8xPKHOCVoYVG82p_zpQXfW7_ERRAphBkZVvhWN8,960
fontTools/__pycache__/__init__.cpython-311.pyc,,
fontTools/__pycache__/__main__.cpython-311.pyc,,
fontTools/__pycache__/afmLib.cpython-311.pyc,,
fontTools/__pycache__/agl.cpython-311.pyc,,
fontTools/__pycache__/fontBuilder.cpython-311.pyc,,
fontTools/__pycache__/help.cpython-311.pyc,,
fontTools/__pycache__/tfmLib.cpython-311.pyc,,
fontTools/__pycache__/ttx.cpython-311.pyc,,
fontTools/__pycache__/unicode.cpython-311.pyc,,
fontTools/afmLib.py,sha256=YbmmjT8Du6qFUhFHwnAhOdvsyfXszODVjSJtd18CCjY,13603
fontTools/agl.py,sha256=4aKwnbvSVUa39eV5Ka8e5ULwV-IEp4pcfwlMwEH_z3k,118208
fontTools/cffLib/CFF2ToCFF.py,sha256=5uPKDFwoJvH0KVDrCjpf3MdOpqbyvdZMe0jZ3emjdsQ,6291
fontTools/cffLib/CFFToCFF2.py,sha256=0dCYSSozptUC9BVUre49e6LgjSxJRtVyMl8vDB6i3r4,10424
fontTools/cffLib/__init__.py,sha256=dts9y-OM8u0-MRsLqQ6WoL2kcDQSNLiYOrQVkd88T8g,110745
fontTools/cffLib/__pycache__/CFF2ToCFF.cpython-311.pyc,,
fontTools/cffLib/__pycache__/CFFToCFF2.cpython-311.pyc,,
fontTools/cffLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/cffLib/__pycache__/specializer.cpython-311.pyc,,
fontTools/cffLib/__pycache__/transforms.cpython-311.pyc,,
fontTools/cffLib/__pycache__/width.cpython-311.pyc,,
fontTools/cffLib/specializer.py,sha256=TTPFdTmtpirYFpTCTbaL-wFL0a3GXwRmjSEbPIWltV0,33468
fontTools/cffLib/transforms.py,sha256=8hffhsWRhBhVukNSL-7ieuygTVV5Ta3Cz9s4s8Awvgg,17861
fontTools/cffLib/width.py,sha256=3L9NWI0uQrJHvHF_IvC_tbW1cq94zgDEPSjubdug8qM,6284
fontTools/colorLib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fontTools/colorLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/colorLib/__pycache__/builder.cpython-311.pyc,,
fontTools/colorLib/__pycache__/errors.cpython-311.pyc,,
fontTools/colorLib/__pycache__/geometry.cpython-311.pyc,,
fontTools/colorLib/__pycache__/table_builder.cpython-311.pyc,,
fontTools/colorLib/__pycache__/unbuilder.cpython-311.pyc,,
fontTools/colorLib/builder.py,sha256=S8z4Qzw2FAE-d1Zm1eHyqDBYh6FW4W_hQJWjVeVicOk,23672
fontTools/colorLib/errors.py,sha256=_3vbGsi6nlkRxxglt82uxK89K8tjURX59G3BBQIy5ps,43
fontTools/colorLib/geometry.py,sha256=RH7sl0oP9othrawGMeLVDAIocv8I2HrMd3aW857Xi_s,5661
fontTools/colorLib/table_builder.py,sha256=0k6SHt8JBwP6hy-nZ9k6VXnPywdPRBe91yZyGq3Mzb8,7692
fontTools/colorLib/unbuilder.py,sha256=nw8YKKiJiSsZAPcvPzRvXO-oZnvWmCWE7Y8nU1g75iE,2223
fontTools/config/__init__.py,sha256=g2dOHV53afHerz-eXt1NKeComFf26ufrZCz1nrRfs0M,2718
fontTools/config/__pycache__/__init__.cpython-311.pyc,,
fontTools/cu2qu/__init__.py,sha256=OoM_nBJAleZal6kxeNJn1ESy1pNm5c3DG417yVIE0-Q,633
fontTools/cu2qu/__main__.py,sha256=6Vb8Ler3yqJ5w84UwlMJV6cS01uhV4PN10OlXQ6jlqo,98
fontTools/cu2qu/__pycache__/__init__.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/__main__.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/benchmark.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/cli.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/cu2qu.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/errors.cpython-311.pyc,,
fontTools/cu2qu/__pycache__/ufo.cpython-311.pyc,,
fontTools/cu2qu/benchmark.py,sha256=FwdvNjKfWHo18_CX0CO8AY5c68XSBE4M4TJo_EkB4q8,1350
fontTools/cu2qu/cli.py,sha256=CvWzC5a6XF_v5o0yrS4vGI1JXiVVLzSJahTIqpJmiPk,6274
fontTools/cu2qu/cu2qu.c,sha256=DGeaocDDXSlWL0vHL553KLiFwXVQ550C5SM5OgimUiU,589442
fontTools/cu2qu/cu2qu.cp311-win_amd64.pyd,sha256=Ht5FnyY2F_wWHUSrhJDEYh76nGicjpXdD22TGgcDKLo,103936
fontTools/cu2qu/cu2qu.py,sha256=XH2bnQ5aG9ic921ZWzQzU1-q3MQU6INCjLk4XjRj5_Y,16970
fontTools/cu2qu/errors.py,sha256=uYyPSs_x-EMJKO2S3cLGWyk_KlHoOoh_XEtdB_oKBp0,2518
fontTools/cu2qu/ufo.py,sha256=Mpd_7Be9jxNcOKFqkyRp8Oem3CS3R-ZYMMSD03LJL6o,12143
fontTools/designspaceLib/__init__.py,sha256=80fzbsWaoTMaXsPGMnevXAxR4eqvZeYCwV_GYpBvlkM,132601
fontTools/designspaceLib/__main__.py,sha256=QOn1SNf8xmw-zQ5EJN0JnrHllu9rbRm8kTpWF9b3jlo,109
fontTools/designspaceLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/designspaceLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/designspaceLib/__pycache__/split.cpython-311.pyc,,
fontTools/designspaceLib/__pycache__/statNames.cpython-311.pyc,,
fontTools/designspaceLib/__pycache__/types.cpython-311.pyc,,
fontTools/designspaceLib/split.py,sha256=MjgyVDfhLEdb844nioL3xIN6VinHqY4jcdOlwmvr03M,19714
fontTools/designspaceLib/statNames.py,sha256=n9EyVpvFfISQbejzs45AHj6kSBHHgFr-8xl9jVmcHng,9322
fontTools/designspaceLib/types.py,sha256=HtM5ibhj1FeoS5Yq2Q5YAlP8CL5WDI_W_0v-qJyKJww,5467
fontTools/encodings/MacRoman.py,sha256=rxWvh1yMTg_pY7_sSKpjfD6bYcA-BVHZL4S8JUH33fc,3834
fontTools/encodings/StandardEncoding.py,sha256=z0Uh0ZLnz5SsO6T2dxN0S646ZYRfpC_F6HtUIsidC94,3839
fontTools/encodings/__init__.py,sha256=QoK6HlOoqtVqX5gOyv0bJiTXsVBbBRreUifdccWNp2k,76
fontTools/encodings/__pycache__/MacRoman.cpython-311.pyc,,
fontTools/encodings/__pycache__/StandardEncoding.cpython-311.pyc,,
fontTools/encodings/__pycache__/__init__.cpython-311.pyc,,
fontTools/encodings/__pycache__/codecs.cpython-311.pyc,,
fontTools/encodings/codecs.py,sha256=bSpO6kuPbEIDsXSVHhzftqsm_FFUiXpLVfPSk410SqE,4856
fontTools/feaLib/__init__.py,sha256=RprjP6BKswq4pt0J-9L1XGuZfjIFAGD6HDly_haMAN4,217
fontTools/feaLib/__main__.py,sha256=niUAPkiYxeRAJMlJuvVJZism2VFufZrNaQtieA7sNLk,2318
fontTools/feaLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/feaLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/feaLib/__pycache__/ast.cpython-311.pyc,,
fontTools/feaLib/__pycache__/builder.cpython-311.pyc,,
fontTools/feaLib/__pycache__/error.cpython-311.pyc,,
fontTools/feaLib/__pycache__/lexer.cpython-311.pyc,,
fontTools/feaLib/__pycache__/location.cpython-311.pyc,,
fontTools/feaLib/__pycache__/lookupDebugInfo.cpython-311.pyc,,
fontTools/feaLib/__pycache__/parser.cpython-311.pyc,,
fontTools/feaLib/__pycache__/variableScalar.cpython-311.pyc,,
fontTools/feaLib/ast.py,sha256=rjEQSs5fFCCUM03RIEJ1iohPv-VPttlkngE_UzTKOL8,75935
fontTools/feaLib/builder.py,sha256=exK2143ntpp9tCKJJXGldqpSLmMdBwerncD_sFzd_9E,72319
fontTools/feaLib/error.py,sha256=PSXeclXORb9hkvXh95e6YrNKZpPPWkekXOJZCSEhSjg,665
fontTools/feaLib/lexer.c,sha256=yKf2WoKLiJJcrI35TuqUxIQYLOLoyr5D2MhcxY8vFF8,750768
fontTools/feaLib/lexer.cp311-win_amd64.pyd,sha256=zd9As8uQ2pmVg20oBhHhqTMjOgE7wBomteGG-CW56G8,141312
fontTools/feaLib/lexer.py,sha256=7VZ3NPFH7V1mvRbym111BNKvbB4hLfGLTMS0VV_3Ipw,11408
fontTools/feaLib/location.py,sha256=teHrhjT8zzImcGBEJS1J43oaX9onCPu_pynxS8d-tUg,246
fontTools/feaLib/lookupDebugInfo.py,sha256=h4Ig8kmEk5WlGf1C9JJAbbOKQK5OwkFLdj8CT7fOkmU,316
fontTools/feaLib/parser.py,sha256=LL5wSiq_qXTJOjtNv2q2EpNyIuA065iWmS-YHCZgfzU,100825
fontTools/feaLib/variableScalar.py,sha256=RiLHKQh2-wa-BZ015H2e7XkbshssTj2PjlapaMNJfAs,4182
fontTools/fontBuilder.py,sha256=lj2qnZ9G_LnHOwbp-uDWLxXXCBMenqK8_pAOWBTSZBg,34960
fontTools/help.py,sha256=8yn5iAonGPsijFSHmU6aLuuZtaLMhR5CIkSp9hVYL2c,1161
fontTools/merge/__init__.py,sha256=6MOtk0FXWmSmZsLf1sfjiN2lteVm-u9tI0RVWFewYHM,8498
fontTools/merge/__main__.py,sha256=3_u3dnyEOyh0O-SrLMLlkXxOfCFT-0SlwJpimosVJ-c,100
fontTools/merge/__pycache__/__init__.cpython-311.pyc,,
fontTools/merge/__pycache__/__main__.cpython-311.pyc,,
fontTools/merge/__pycache__/base.cpython-311.pyc,,
fontTools/merge/__pycache__/cmap.cpython-311.pyc,,
fontTools/merge/__pycache__/layout.cpython-311.pyc,,
fontTools/merge/__pycache__/options.cpython-311.pyc,,
fontTools/merge/__pycache__/tables.cpython-311.pyc,,
fontTools/merge/__pycache__/unicode.cpython-311.pyc,,
fontTools/merge/__pycache__/util.cpython-311.pyc,,
fontTools/merge/base.py,sha256=LPJKOwMiDwayLGzA1xH325CtYHPvahAA17lihvKjiPw,2470
fontTools/merge/cmap.py,sha256=jnWRpxy1Y8J6vcCnnjW3vMWbxL89FbqNgY5FzP1Z1PY,5686
fontTools/merge/layout.py,sha256=S9j0FOUDOtXAzfO7_L6IrLBHplSLfxFqIi_IJUunXCg,16601
fontTools/merge/options.py,sha256=b-9GZ-nN7fh1VrpnEFhK_eRZPIIlRArtYOndOCetoUY,2586
fontTools/merge/tables.py,sha256=qEvXhTZE4tEOmsst8_YKsC6Pvl18VlUr90Fujh5b_gg,10981
fontTools/merge/unicode.py,sha256=mgqRFhRugda62Xt0r28SduaN7YBzRfHxrpNprjLqoX8,4351
fontTools/merge/util.py,sha256=3alo4b7mhFNC6h8PjeqNU99dS7EuO8sdZkZpvRsEE6E,3521
fontTools/misc/__init__.py,sha256=QoK6HlOoqtVqX5gOyv0bJiTXsVBbBRreUifdccWNp2k,76
fontTools/misc/__pycache__/__init__.cpython-311.pyc,,
fontTools/misc/__pycache__/arrayTools.cpython-311.pyc,,
fontTools/misc/__pycache__/bezierTools.cpython-311.pyc,,
fontTools/misc/__pycache__/classifyTools.cpython-311.pyc,,
fontTools/misc/__pycache__/cliTools.cpython-311.pyc,,
fontTools/misc/__pycache__/configTools.cpython-311.pyc,,
fontTools/misc/__pycache__/cython.cpython-311.pyc,,
fontTools/misc/__pycache__/dictTools.cpython-311.pyc,,
fontTools/misc/__pycache__/eexec.cpython-311.pyc,,
fontTools/misc/__pycache__/encodingTools.cpython-311.pyc,,
fontTools/misc/__pycache__/etree.cpython-311.pyc,,
fontTools/misc/__pycache__/filenames.cpython-311.pyc,,
fontTools/misc/__pycache__/fixedTools.cpython-311.pyc,,
fontTools/misc/__pycache__/intTools.cpython-311.pyc,,
fontTools/misc/__pycache__/iterTools.cpython-311.pyc,,
fontTools/misc/__pycache__/lazyTools.cpython-311.pyc,,
fontTools/misc/__pycache__/loggingTools.cpython-311.pyc,,
fontTools/misc/__pycache__/macCreatorType.cpython-311.pyc,,
fontTools/misc/__pycache__/macRes.cpython-311.pyc,,
fontTools/misc/__pycache__/psCharStrings.cpython-311.pyc,,
fontTools/misc/__pycache__/psLib.cpython-311.pyc,,
fontTools/misc/__pycache__/psOperators.cpython-311.pyc,,
fontTools/misc/__pycache__/py23.cpython-311.pyc,,
fontTools/misc/__pycache__/roundTools.cpython-311.pyc,,
fontTools/misc/__pycache__/sstruct.cpython-311.pyc,,
fontTools/misc/__pycache__/symfont.cpython-311.pyc,,
fontTools/misc/__pycache__/testTools.cpython-311.pyc,,
fontTools/misc/__pycache__/textTools.cpython-311.pyc,,
fontTools/misc/__pycache__/timeTools.cpython-311.pyc,,
fontTools/misc/__pycache__/transform.cpython-311.pyc,,
fontTools/misc/__pycache__/treeTools.cpython-311.pyc,,
fontTools/misc/__pycache__/vector.cpython-311.pyc,,
fontTools/misc/__pycache__/visitor.cpython-311.pyc,,
fontTools/misc/__pycache__/xmlReader.cpython-311.pyc,,
fontTools/misc/__pycache__/xmlWriter.cpython-311.pyc,,
fontTools/misc/arrayTools.py,sha256=baENNALPvYRUhS4rdx_F3ltOmVIf1PV9G2EaMt7gAHM,11907
fontTools/misc/bezierTools.c,sha256=6GXbwsUuu68dBOek0XIdZTM4S3qI35B-0v80SPHBXmo,1802154
fontTools/misc/bezierTools.cp311-win_amd64.pyd,sha256=4yRmMnuxMmKqtG6wepD-hXK6sbl_W7in13vjWxFbNgE,379904
fontTools/misc/bezierTools.py,sha256=pGC_2kFPjSftWVm7DJTh9xxfddUw2DkBPFn4779iq1o,46221
fontTools/misc/classifyTools.py,sha256=wLTjOhLiZaLiwwUTj2Ad5eZ5T_38W0Eo_uzRGWHWYvE,5783
fontTools/misc/cliTools.py,sha256=7zKOXczaCKRMW6Yv5jdCZYHco8y0-lfimhIWzQ2IL8A,1915
fontTools/misc/configTools.py,sha256=JNR7HqId8zudAlFcK4lwocHZkwgaTSH4u6BOyFLTujw,11537
fontTools/misc/cython.py,sha256=fZ9_mObkVzdJoK6sufiIU95k5GStjp6LWOk4AQ8zW_Q,709
fontTools/misc/dictTools.py,sha256=GZa83GxwQD4-kZYkbCCefW-ggH4WG8G6f5jCy0NcO6w,2500
fontTools/misc/eexec.py,sha256=eN9R1_67tWaeWn3ikEs0VwB1N7yr4vBbzs-aMbAUROw,3450
fontTools/misc/encodingTools.py,sha256=rlAZpxgcKXPzfpfHKk0BQW2Edz2JwTT8d0IIMRib3VE,2145
fontTools/misc/etree.py,sha256=4208zAz7_-wMclnXu8abPlmgmkmTPqWekTOJ6JNCCuA,17558
fontTools/misc/filenames.py,sha256=IZuoPgh88KI2Rdo56FrHAtNSUoCeIaiWqrQk2VEeRoQ,8468
fontTools/misc/fixedTools.py,sha256=3HzMFAs57LqsGBnbslq2btQ3KJbKwxmxkJPvTvOi8sY,7900
fontTools/misc/intTools.py,sha256=kRNjD5_2jyTKo07C0sFT0jT3dcVnU5XGJEjbXCErm4E,611
fontTools/misc/iterTools.py,sha256=hyLQrAPuUOzDoQWKtKhFLjV8-Gx3jHd9SvBEwQRSeTE,402
fontTools/misc/lazyTools.py,sha256=LJ7QvDG65xOBw2AI43qGCLxVmfdbsf-PUECfrenbkAU,1062
fontTools/misc/loggingTools.py,sha256=iUvIUZPoxwYLlxaaprHpqBO4143P_nRthalGqrd3lGQ,20441
fontTools/misc/macCreatorType.py,sha256=5JZKTsnkI_VBhC52lwMSrdmzqgUOhwC42jPvbGahsPo,1649
fontTools/misc/macRes.py,sha256=ewiYDKioxxBKW6JQcRmxpNYw5JgtJZIJyqWBG_KplUo,8840
fontTools/misc/plistlib/__init__.py,sha256=doPqlGry1mRywSup0ahnwuT7mNeClhYQ82y7kd86hWQ,21794
fontTools/misc/plistlib/__pycache__/__init__.cpython-311.pyc,,
fontTools/misc/plistlib/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fontTools/misc/psCharStrings.py,sha256=e5kR55Gm3orJsDLo3eu6CxpoZ1pMNZh5Wm-Zj4m7wJs,44532
fontTools/misc/psLib.py,sha256=cqxG8yMZ7_5VTxgTUl2ARNhIhNu_iTzxLTEd1Egwugo,12497
fontTools/misc/psOperators.py,sha256=9nZ4ymbiiCApY9V8OARpYqvO73OEcJgGyTtCuGzD-rw,16272
fontTools/misc/py23.py,sha256=BhByQabxZis6fDvK3ZVeI-YRj_1rMQeBZCFzGWIac0U,2334
fontTools/misc/roundTools.py,sha256=2rmbuk73NYGPmJqP58FQCFioSLilvNffd0WbL5znKUg,3283
fontTools/misc/sstruct.py,sha256=RG8qOzTkp9LIN5bis5XkbA-6amnuv2Pi-foZTzIQRRE,7389
fontTools/misc/symfont.py,sha256=ZxyD-mipj7raOtXDdCakpwoSo0hsKPJXLlp3OBPHraE,7235
fontTools/misc/testTools.py,sha256=OhWQmnuWrgJWtME_9fHG7Npd1JzVktCPcbHiccxL2wI,7162
fontTools/misc/textTools.py,sha256=NIBmM6k9PXIs8DMpio-9ckHS35QxL2EMFwBXP6zG-8w,3531
fontTools/misc/timeTools.py,sha256=lmncKUKvxQKO4Kqx2k7UNFkYYpj2n5CwR1lPiLZv3tA,2322
fontTools/misc/transform.py,sha256=WZ9mVAEBqcyhCCUmLLS-hDuiFgppQYvK3e-jESkugGY,16136
fontTools/misc/treeTools.py,sha256=IMopMUcuhelvz8gNra50Zc1w8DSlWywnL6DFaz1ijQs,1314
fontTools/misc/vector.py,sha256=yaNixq5pXXpPCD_wRP-LsXYSLr4WPX_y92Po05FeLU0,4209
fontTools/misc/visitor.py,sha256=h35A4SWww-MNII7f68bc9pj1-2poTxztvVpKH5EEtEE,5456
fontTools/misc/xmlReader.py,sha256=gqYg3qlDkrKsO55DPaJ-dU0i5rltqZgnKlrXmR2Z7dQ,6768
fontTools/misc/xmlWriter.py,sha256=3gHeiyhbXDqDK-jn44f4znND3nEPWnk2Bdlm2Y8JZYo,6250
fontTools/mtiLib/__init__.py,sha256=9hJjW26NnWMQA9OWZraBbBpiKvF3FxrBUauCCWxJfHM,48019
fontTools/mtiLib/__main__.py,sha256=MnVcMQ1TxmYged20wKcjrpZDIvetmkzfRVKHCb5dsUc,99
fontTools/mtiLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/mtiLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/otlLib/__init__.py,sha256=WhTONAtlItZxWAkHNit_EBW19pP32TFZSqIJ_GG6Peg,46
fontTools/otlLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/otlLib/__pycache__/builder.cpython-311.pyc,,
fontTools/otlLib/__pycache__/error.cpython-311.pyc,,
fontTools/otlLib/__pycache__/maxContextCalc.cpython-311.pyc,,
fontTools/otlLib/builder.py,sha256=hWNIpdqT4P3NoFZGBR3IKVpus3OzSekNjd08cDGlax0,123525
fontTools/otlLib/error.py,sha256=0OQ2AuxKNEqvoHIkgouf47LDGDEmPUlhdZIW5DROL8k,346
fontTools/otlLib/maxContextCalc.py,sha256=sVU7LLwkjhV16ADcpjbUwCt5PZbWWdc8_yZo9Lv7HaI,3271
fontTools/otlLib/optimize/__init__.py,sha256=NKqA7fqHyzjkmuBL_ZVpc3u9OMbWxbKDtymC8CnVGNY,1583
fontTools/otlLib/optimize/__main__.py,sha256=ZZDwg21yVtdQi9GkNQe70w49hn9fPmObFEEDWGlCj3U,110
fontTools/otlLib/optimize/__pycache__/__init__.cpython-311.pyc,,
fontTools/otlLib/optimize/__pycache__/__main__.cpython-311.pyc,,
fontTools/otlLib/optimize/__pycache__/gpos.cpython-311.pyc,,
fontTools/otlLib/optimize/gpos.py,sha256=I3WwdzPGAjcRiHABfKIt0ral7ik2Qk4CtBPP4J4tEso,18927
fontTools/pens/__init__.py,sha256=QoK6HlOoqtVqX5gOyv0bJiTXsVBbBRreUifdccWNp2k,76
fontTools/pens/__pycache__/__init__.cpython-311.pyc,,
fontTools/pens/__pycache__/areaPen.cpython-311.pyc,,
fontTools/pens/__pycache__/basePen.cpython-311.pyc,,
fontTools/pens/__pycache__/boundsPen.cpython-311.pyc,,
fontTools/pens/__pycache__/cairoPen.cpython-311.pyc,,
fontTools/pens/__pycache__/cocoaPen.cpython-311.pyc,,
fontTools/pens/__pycache__/cu2quPen.cpython-311.pyc,,
fontTools/pens/__pycache__/explicitClosingLinePen.cpython-311.pyc,,
fontTools/pens/__pycache__/filterPen.cpython-311.pyc,,
fontTools/pens/__pycache__/freetypePen.cpython-311.pyc,,
fontTools/pens/__pycache__/hashPointPen.cpython-311.pyc,,
fontTools/pens/__pycache__/momentsPen.cpython-311.pyc,,
fontTools/pens/__pycache__/perimeterPen.cpython-311.pyc,,
fontTools/pens/__pycache__/pointInsidePen.cpython-311.pyc,,
fontTools/pens/__pycache__/pointPen.cpython-311.pyc,,
fontTools/pens/__pycache__/qtPen.cpython-311.pyc,,
fontTools/pens/__pycache__/qu2cuPen.cpython-311.pyc,,
fontTools/pens/__pycache__/quartzPen.cpython-311.pyc,,
fontTools/pens/__pycache__/recordingPen.cpython-311.pyc,,
fontTools/pens/__pycache__/reportLabPen.cpython-311.pyc,,
fontTools/pens/__pycache__/reverseContourPen.cpython-311.pyc,,
fontTools/pens/__pycache__/roundingPen.cpython-311.pyc,,
fontTools/pens/__pycache__/statisticsPen.cpython-311.pyc,,
fontTools/pens/__pycache__/svgPathPen.cpython-311.pyc,,
fontTools/pens/__pycache__/t2CharStringPen.cpython-311.pyc,,
fontTools/pens/__pycache__/teePen.cpython-311.pyc,,
fontTools/pens/__pycache__/transformPen.cpython-311.pyc,,
fontTools/pens/__pycache__/ttGlyphPen.cpython-311.pyc,,
fontTools/pens/__pycache__/wxPen.cpython-311.pyc,,
fontTools/pens/areaPen.py,sha256=SJnD7HwRg6JL_p7HaAy5DB64G75So9sqIdmzCSRv1bI,1524
fontTools/pens/basePen.py,sha256=Wrd4xNl2apH4fdpkCPbV8z0QuNX7k46JHwylZer72G0,17548
fontTools/pens/boundsPen.py,sha256=JPqvmslPlv2kgdmhgjeJo-CTYbloxxkkaJD8wVTVpng,3227
fontTools/pens/cairoPen.py,sha256=jQL-9usqCU_FvfFpH4uaKjOcGd6jsarPpVM3vrhdyOU,618
fontTools/pens/cocoaPen.py,sha256=ReJkXzlgP8qe4zi_6X4oO_I6m0jQGATeB6ZHjJhNv_I,638
fontTools/pens/cu2quPen.py,sha256=w9xTNmhb96kvNZwcM5WT9q8FnRgA51AOISzVRpkiI3g,13332
fontTools/pens/explicitClosingLinePen.py,sha256=knCXcjSl2iPy6mLCDnsdDYx6J5rV7FH4S24OXFdINjg,3320
fontTools/pens/filterPen.py,sha256=tWhgklyaCTUt7oQRTBbFUcOlc702V0NfadCH3X93CYg,8031
fontTools/pens/freetypePen.py,sha256=NqNzXrOTDckoH4N6WLnj-KuxGcg6z7DlqSCfmpq8qAE,20370
fontTools/pens/hashPointPen.py,sha256=ZAU87uw5ge3Kb4i9kRV28a5VFeZ_TWSsJabyAzwAHrU,3662
fontTools/pens/momentsPen.c,sha256=ajNsDnHtKVFHde329jgP1R_dCoAhd_lJF3oFrtSlaX8,530428
fontTools/pens/momentsPen.cp311-win_amd64.pyd,sha256=fLg9ohQqxTcpBv446Cwbn5-F912wZmN9AtkcgEnSJ4U,95232
fontTools/pens/momentsPen.py,sha256=Z-V5CjQBSj3qPxg3C_DBFKExqno89nOe3jWwHT9_xsM,26537
fontTools/pens/perimeterPen.py,sha256=Zy5F8QzaNJAkkQQSb2QJCp-wZTvDAjBn-B099t2ABds,2222
fontTools/pens/pointInsidePen.py,sha256=Hy48iR5NWV3x_wWoos-UC7GMtwvvUhd_q_ykiwaWdzQ,6547
fontTools/pens/pointPen.py,sha256=iB5vNauqHSeCPHKVBHXg6RAyC_e8Rmuaeldjwb-hpG8,22896
fontTools/pens/qtPen.py,sha256=KHHQggFQc6Gq-kPdn9X2_wBXTPWzvyzKTSUeq0mqvSM,663
fontTools/pens/qu2cuPen.py,sha256=VIqUzA_y_6xnRmTESKzlKkoByh7ZU5TnQwHnVAoy4no,4090
fontTools/pens/quartzPen.py,sha256=6DMDWPYfsOb374VDnLLpKLqcMJig4GCGbTsW1Jr0fgg,1330
fontTools/pens/recordingPen.py,sha256=hw393TStvhoF1XT7aidpVQ8glASbxZuARnUAyUyZAGM,12824
fontTools/pens/reportLabPen.py,sha256=vVRG044LvUvFtqrRFYRiMFS_USHAeAvz9y9-7__WbY4,2145
fontTools/pens/reverseContourPen.py,sha256=E_Ny86JfiMoQ04VfswMtdpaKCU37wNy9ifOccb0aWKQ,4118
fontTools/pens/roundingPen.py,sha256=AHC1J0dgRChtFmkkgeR1D1ZNFUoHZTcHpWRIyL5d1_Q,4779
fontTools/pens/statisticsPen.py,sha256=zVMmaCHxiMnmdghb2yDKPkSSPUxs2JYMjepXP4I_6iA,9947
fontTools/pens/svgPathPen.py,sha256=4aU4iTlnGuzzyXrBgfHvrjMOkC2rdSF8HOkJ_q8tZ38,8882
fontTools/pens/t2CharStringPen.py,sha256=vG6gmTe2bWc-bCn4-LQgv1f16p62oNWJUn8FiXTl0EM,2459
fontTools/pens/teePen.py,sha256=19N3FEaFm4mGMTZrEn5Qg4YiXGGK61zcXjh2LcRxe_s,1345
fontTools/pens/transformPen.py,sha256=_Zvyxp0yQ7iFZ1_FYfr3KFWKWYOUY2eSxrRk41BRO2w,4171
fontTools/pens/ttGlyphPen.py,sha256=gAglwTL9DSsJGI8TUPVz-YBdPSMUcvd2S9jF-FzmckE,12205
fontTools/pens/wxPen.py,sha256=bolMLl06Q-TxsN8-SsSDbmJStTPGXMYJQZ7Vb67FhLw,709
fontTools/qu2cu/__init__.py,sha256=MpdE0XsHSDo9M3hyHLkPPLxB3FKr3aiT0dPW5qHCuSo,633
fontTools/qu2cu/__main__.py,sha256=leKpToUNNyHf0nobr1I19vus2ziA1pO7rRKkreat-Xw,100
fontTools/qu2cu/__pycache__/__init__.cpython-311.pyc,,
fontTools/qu2cu/__pycache__/__main__.cpython-311.pyc,,
fontTools/qu2cu/__pycache__/benchmark.cpython-311.pyc,,
fontTools/qu2cu/__pycache__/cli.cpython-311.pyc,,
fontTools/qu2cu/__pycache__/qu2cu.cpython-311.pyc,,
fontTools/qu2cu/benchmark.py,sha256=PFxx2Bfu7-KuNrzdOIBXHPZvyNphqqcTVy4CneaCo3M,1456
fontTools/qu2cu/cli.py,sha256=1QLBTSZW7e_VATJN9vjszRxIk_-Xjxu1KP53yX4T7q8,3839
fontTools/qu2cu/qu2cu.c,sha256=xLksDndkY4iCqLrFx9a6XOFL3mWNdDw1kdpiu16sidg,654946
fontTools/qu2cu/qu2cu.cp311-win_amd64.pyd,sha256=aNURiPNX9vxqYPFb-4scKhxoyqSQUbCI6KoK_d37QgI,115712
fontTools/qu2cu/qu2cu.py,sha256=dtp5Zqhcs_NePwA2U5fgG2LtWleRwmBilTurau8sLL0,12693
fontTools/subset/__init__.py,sha256=a0OHXLf9m8qlXRFA7Gxp5qxe8ImUQ5ziK3bD-4fm618,137743
fontTools/subset/__main__.py,sha256=cEIC52EtGOJvFDfHXzi0M2EAYmyHAcI-ZZ0lb2y4r7s,101
fontTools/subset/__pycache__/__init__.cpython-311.pyc,,
fontTools/subset/__pycache__/__main__.cpython-311.pyc,,
fontTools/subset/__pycache__/cff.cpython-311.pyc,,
fontTools/subset/__pycache__/svg.cpython-311.pyc,,
fontTools/subset/__pycache__/util.cpython-311.pyc,,
fontTools/subset/cff.py,sha256=GSmxdsokxuFKvJJQVcAIOhd5hYQq8KkzxnXE_dgm8yo,6329
fontTools/subset/svg.py,sha256=y_yTZuAm3bjcoEOFu5likXoHuG5u1oNiv0mOni2Z9fQ,9637
fontTools/subset/util.py,sha256=gh2hkLaUmhHKRkdxxdLcFjz8clCmncLqdnDZm_2QNco,779
fontTools/svgLib/__init__.py,sha256=2igTH8FIxCzEp02sRijWni-ocuGqqwuPPPSpgjozrK0,78
fontTools/svgLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/svgLib/path/__init__.py,sha256=xfTh9zD_JOjEq6EEDtDxYCtn73O33d5wCIaVEfsIb0U,2061
fontTools/svgLib/path/__pycache__/__init__.cpython-311.pyc,,
fontTools/svgLib/path/__pycache__/arc.cpython-311.pyc,,
fontTools/svgLib/path/__pycache__/parser.cpython-311.pyc,,
fontTools/svgLib/path/__pycache__/shapes.cpython-311.pyc,,
fontTools/svgLib/path/arc.py,sha256=-jU7F3gO_DdTO6MrDbOLxmFBZ_h5eb02Eq3Z_Ia35Nw,5966
fontTools/svgLib/path/parser.py,sha256=mMxmJjU1Z9beD0CqFrvBx9LkCutJ2LfKbTLgidLQvNw,11110
fontTools/svgLib/path/shapes.py,sha256=h3aOhsZ0pPUOLtNab2bj5cJuqPIlgdtOOOT4VYvnRww,5505
fontTools/t1Lib/__init__.py,sha256=eBp3X5XcHZIV4uurKxyakurcT2bfFdoTVpw4AOMx2TU,21513
fontTools/t1Lib/__pycache__/__init__.cpython-311.pyc,,
fontTools/tfmLib.py,sha256=-bv4iv2VhUSse5pA0oXdudf7o7ZuFWdWNsiHElO06dk,14730
fontTools/ttLib/__init__.py,sha256=d2D4Zy6tLXDeQtYUa5jKfIiLoKdIRgMB2F3FnVnv7XM,579
fontTools/ttLib/__main__.py,sha256=MscZr4z7ej6EOfru-rlCOFJSIoN6TJQfGgzlkh4kOh0,3552
fontTools/ttLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/ttLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/ttLib/__pycache__/macUtils.cpython-311.pyc,,
fontTools/ttLib/__pycache__/removeOverlaps.cpython-311.pyc,,
fontTools/ttLib/__pycache__/reorderGlyphs.cpython-311.pyc,,
fontTools/ttLib/__pycache__/scaleUpem.cpython-311.pyc,,
fontTools/ttLib/__pycache__/sfnt.cpython-311.pyc,,
fontTools/ttLib/__pycache__/standardGlyphOrder.cpython-311.pyc,,
fontTools/ttLib/__pycache__/ttCollection.cpython-311.pyc,,
fontTools/ttLib/__pycache__/ttFont.cpython-311.pyc,,
fontTools/ttLib/__pycache__/ttGlyphSet.cpython-311.pyc,,
fontTools/ttLib/__pycache__/ttVisitor.cpython-311.pyc,,
fontTools/ttLib/__pycache__/woff2.cpython-311.pyc,,
fontTools/ttLib/macUtils.py,sha256=B5UhZU8gQerJMXEG9-BGZsuv3aewFRAGQ5HCuZMzMkQ,1791
fontTools/ttLib/removeOverlaps.py,sha256=PTxICjLx89JxKfboLruoV_OwuwCIxcJ4feNcCCkrsTQ,13005
fontTools/ttLib/reorderGlyphs.py,sha256=_sTg1wR9q8ZFeo4n7H7iUcqGIWOroqDQd_UbZ-1kZh0,10600
fontTools/ttLib/scaleUpem.py,sha256=Qz-kS48q7a5GibgnPoUglyVk_qIVkYp5KZ-r1aMx_7Q,15054
fontTools/ttLib/sfnt.py,sha256=7X9xujgV0Za4nOEfUD3mSrrRb-f9NuzEqgJ-IFLNVQU,23494
fontTools/ttLib/standardGlyphOrder.py,sha256=VG-8hW1VgQIro7cDJusSXThILIr4pQgmU37t85SQ65Y,6056
fontTools/ttLib/tables/B_A_S_E_.py,sha256=KpUf8_XEoFNEv3RcoQjfOaJUtBBaCxMzfifEcGtAydI,383
fontTools/ttLib/tables/BitmapGlyphMetrics.py,sha256=cQuhook-kYL6AoUS9vQIAr65Ls6xN-e15l_lCxDwM2w,1833
fontTools/ttLib/tables/C_B_D_T_.py,sha256=zg-Knjto2WgnEjl-_foLbeecNp0KHUOHg8ZgCmAyCqI,3759
fontTools/ttLib/tables/C_B_L_C_.py,sha256=b8FTbwHE4RinasyZ1ieLW7lo5gmmWQB3fO73g4DMVAE,539
fontTools/ttLib/tables/C_F_F_.py,sha256=Jo_pbWzq6im8Jh4N47RTl9E6-6YQbRiNI2mOY-JY8Js,2039
fontTools/ttLib/tables/C_F_F__2.py,sha256=q9Y6-yvA8JjrMjBKvb0jtg5T0Z1qKPBduTv8A5-WTZk,833
fontTools/ttLib/tables/C_O_L_R_.py,sha256=pq9xotYUq19Gq8GghKqUlru0nBqlmKdAcqrxa25wboM,6158
fontTools/ttLib/tables/C_P_A_L_.py,sha256=DEB9H9TXDg9uYhmrNEsNgXJ6cj9NMCVpVDJ-XMsBJzo,12247
fontTools/ttLib/tables/D_S_I_G_.py,sha256=ngvrE19I7s5t6N3QUsvoUONkTvdVtGC7vItIVDun0r4,5675
fontTools/ttLib/tables/D__e_b_g.py,sha256=DWTQGGhFoqzwaXZVF5Je2BIMtjXBDAPoVes9Jq_3vI8,460
fontTools/ttLib/tables/DefaultTable.py,sha256=_pMaYi_MrvHzioY5s3NvKdzEFaueppMeJIpnfQDwWqg,1536
fontTools/ttLib/tables/E_B_D_T_.py,sha256=f96YN8zj5Qcp9kweWU0fmG1W-uUewNxLR8Ox3yzsnjo,33369
fontTools/ttLib/tables/E_B_L_C_.py,sha256=ED5j8COGRPBUXxoey7bXVoRNiZCC2rgx0cWls2YNp6g,30772
fontTools/ttLib/tables/F_F_T_M_.py,sha256=wpjIN0MfovCM0JEHdzC7ZCTRjBx9-mNjIMRRQGYstCA,1735
fontTools/ttLib/tables/F__e_a_t.py,sha256=JgTjN_z2Wo55Ul6hNGqluRu4zgegtnu2H8mvq1hNjfs,5632
fontTools/ttLib/tables/G_D_E_F_.py,sha256=AjrMHUMYg75zrZloeRbuMXW1VJkYei1iouYpIVZ_mgk,312
fontTools/ttLib/tables/G_M_A_P_.py,sha256=6_EJEwWdE4Jz6Y2BsRNLpGJPbcuozKMAUSMGhqVqWuc,4868
fontTools/ttLib/tables/G_P_K_G_.py,sha256=GBwAX4zOC5fAcK7m9bC2Cf_8kcVu-39tdFUaSYH0jFg,4779
fontTools/ttLib/tables/G_P_O_S_.py,sha256=TU0AI44SJonvGkfF9GO7vH3Ca0R8_DhHDSn5CDUbOfI,411
fontTools/ttLib/tables/G_S_U_B_.py,sha256=x09o8a8tcnGHdbW--RgA7tDao860uh3Lp183DkeMWpc,307
fontTools/ttLib/tables/G__l_a_t.py,sha256=xmyj4nsf1cpYxBAXrvaZ9zY_G1gclGWpfG1m6qOzgw4,8880
fontTools/ttLib/tables/G__l_o_c.py,sha256=sTNUnvHMvFSiU1QOhLT9A8Fw0mTOOyUumhxAOEpB4So,2770
fontTools/ttLib/tables/H_V_A_R_.py,sha256=sVJ4MK33ZenyUq8Hg-tmqu_FlR7tJOsqZgpbUnIQL6E,326
fontTools/ttLib/tables/J_S_T_F_.py,sha256=Pp8tE_w6YNJaCCnzteYQ7B70pZ1_q1nGci4zfASt-4Q,328
fontTools/ttLib/tables/L_T_S_H_.py,sha256=pgoHEK-9iBRmAfjO9lYROT5cqOMxsQjO13MMXU0RXp4,2247
fontTools/ttLib/tables/M_A_T_H_.py,sha256=gU7yDMPDZ_XyA_pYZBYoA_p8rxzJtB65CrNV4Ta35tI,355
fontTools/ttLib/tables/M_E_T_A_.py,sha256=E_jO_lkeLBuGEtlYfH1sDbzIZhP0ZaJX4u3lgF3ZAMs,12341
fontTools/ttLib/tables/M_V_A_R_.py,sha256=sKe1GfahViCwY4kFXSU8t8WYH-FzUOwZdO_q6NnQZbM,321
fontTools/ttLib/tables/O_S_2f_2.py,sha256=HFVzQYVZDqETgRCTbRqa3NyyuwlTwmc6CNSngjt89ZY,28782
fontTools/ttLib/tables/S_I_N_G_.py,sha256=6deN-m2-k5C20NE8iTdajju8D2Mw_0tcPiHQln_RaMo,3416
fontTools/ttLib/tables/S_T_A_T_.py,sha256=clj8sbU60dzo16KApGXNp54CSS387GjIjxuiu5cU09c,513
fontTools/ttLib/tables/S_V_G_.py,sha256=O6Aik0j7t02ODsZRwI_tJUwNJQiZ3Dl3oxPqQhyRXH8,7899
fontTools/ttLib/tables/S__i_l_f.py,sha256=nXCNpzLpeKDruzavbQVQ_VYLtIB0Yq-i46iMqPClIwg,36027
fontTools/ttLib/tables/S__i_l_l.py,sha256=LeN6U0y4VLNgtIa7uCX_cpsZW1Ue_yKY8dsZJyS75ec,3316
fontTools/ttLib/tables/T_S_I_B_.py,sha256=zbtLbMfCSVRd9hc5qoxPoQ8j3tNYtrvofTy7Kl6TBwE,354
fontTools/ttLib/tables/T_S_I_C_.py,sha256=xpE9EYI3hFETe0CFG4RMe4G52_2aBsOs9kiCwXISKeo,395
fontTools/ttLib/tables/T_S_I_D_.py,sha256=wYcFELNUSziSax21UlqOnEBpVl4k4aDOXBYI9F3NwMk,354
fontTools/ttLib/tables/T_S_I_J_.py,sha256=3Q-tPCl04mggf5bIY6p6RvV2ZUmVMfCQ5WYyfJdPfPA,354
fontTools/ttLib/tables/T_S_I_P_.py,sha256=4h7p-ssF_gklUJHtkPy62eA8LvidV38K3HHmiJ2p0ek,354
fontTools/ttLib/tables/T_S_I_S_.py,sha256=eDzfFEZHN4-sawGAF5gtAIj8LKRAFXe8z1ve7aHmY9M,354
fontTools/ttLib/tables/T_S_I_V_.py,sha256=IX-V7mRFxXNmj-wtEfFvpDjkevGZE-OEo5-Dvd6jfgY,881
fontTools/ttLib/tables/T_S_I__0.py,sha256=paQ-mbUq3L27nDmpasrCVVmxMTFFnN1hpNHWpbLu5m0,2178
fontTools/ttLib/tables/T_S_I__1.py,sha256=-QZug_3dTIAYU25tXrig3gOF7RUvZr_ygjg7jZYajUM,7224
fontTools/ttLib/tables/T_S_I__2.py,sha256=QU05Fvz1L-OE7bqXwsRuMjrhtGwi7WtI-UwS7lBS1jM,513
fontTools/ttLib/tables/T_S_I__3.py,sha256=bBo8nZ2bXDcKAmym1rRM--4nrjSXl7CvQhqe6h544SY,565
fontTools/ttLib/tables/T_S_I__5.py,sha256=OJ_fPjJOsJ1DCBsr0oIMEleXAF_FsbWZn0cOxVCmXlM,1635
fontTools/ttLib/tables/T_T_F_A_.py,sha256=2lCDLx_UmkWTvNLrKkDm7T4MErKOvojLDvr6iQ9tXpM,406
fontTools/ttLib/tables/TupleVariation.py,sha256=m7TWmB4TBmJ6DD_onpYidGp5qq3ogTd4qYRXa8XHJ90,33119
fontTools/ttLib/tables/V_A_R_C_.py,sha256=6CgniBLKLlrLXRqDC-z6aYHQD1QzXZYF8w9DMof1PMc,301
fontTools/ttLib/tables/V_D_M_X_.py,sha256=gDeNfw0f1YzJqdad4NSuP5KzuoTbH5bP4GFQOKv58i0,10686
fontTools/ttLib/tables/V_O_R_G_.py,sha256=s9g03_qeTV3qoJAWpXxpRCmao0l1wj4WagR_YsTlyBQ,6130
fontTools/ttLib/tables/V_V_A_R_.py,sha256=PiwzLv95tfXH25hYQFAxL11mwgbLjeg4R1LvVH5m7lU,332
fontTools/ttLib/tables/__init__.py,sha256=JnYOqg1ncoqezSZ6mya0eLInkA0k5tJZ1RLovTgE83M,2721
fontTools/ttLib/tables/__pycache__/B_A_S_E_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/BitmapGlyphMetrics.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_B_D_T_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_B_L_C_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_F_F_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_F_F__2.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_O_L_R_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/C_P_A_L_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/D_S_I_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/D__e_b_g.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/DefaultTable.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/E_B_D_T_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/E_B_L_C_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/F_F_T_M_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/F__e_a_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_D_E_F_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_M_A_P_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_P_K_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_P_O_S_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G_S_U_B_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G__l_a_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/G__l_o_c.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/H_V_A_R_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/J_S_T_F_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/L_T_S_H_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/M_A_T_H_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/M_E_T_A_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/M_V_A_R_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/O_S_2f_2.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S_I_N_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S_T_A_T_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S_V_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S__i_l_f.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/S__i_l_l.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_B_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_C_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_D_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_J_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_P_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_S_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I_V_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__0.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__1.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__2.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__3.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_S_I__5.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/T_T_F_A_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/TupleVariation.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/V_A_R_C_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/V_D_M_X_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/V_O_R_G_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/V_V_A_R_.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/__init__.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_a_n_k_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_a_v_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_b_s_l_n.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_c_i_d_g.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_c_m_a_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_c_v_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_c_v_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_f_e_a_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_f_p_g_m.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_f_v_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_g_a_s_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_g_c_i_d.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_g_l_y_f.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_g_v_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_h_d_m_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_h_e_a_d.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_h_h_e_a.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_h_m_t_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_k_e_r_n.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_l_c_a_r.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_l_o_c_a.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_l_t_a_g.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_m_a_x_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_m_e_t_a.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_m_o_r_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_m_o_r_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_n_a_m_e.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_o_p_b_d.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_p_o_s_t.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_p_r_e_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_p_r_o_p.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_s_b_i_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_t_r_a_k.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_v_h_e_a.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/_v_m_t_x.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/asciiTable.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/grUtils.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otBase.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otConverters.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otData.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otTables.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/otTraverse.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/sbixGlyph.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/sbixStrike.cpython-311.pyc,,
fontTools/ttLib/tables/__pycache__/ttProgram.cpython-311.pyc,,
fontTools/ttLib/tables/_a_n_k_r.py,sha256=eiy6DKxPGw-H9QCLWIQBFveuTFQSKgcPItwgyBOghk8,498
fontTools/ttLib/tables/_a_v_a_r.py,sha256=cIqfWyyU5hNrJ-SIfVCKQcpyi2DUjobAgrmyE2In9FI,7307
fontTools/ttLib/tables/_b_s_l_n.py,sha256=iHLFy3sjFFoGa-pDGdcLCCudv4QFMt2VBL06gigGA_k,480
fontTools/ttLib/tables/_c_i_d_g.py,sha256=BPa6b0yrmT8OXPW3USRpn_H8DOLKFlDe9subtRDJBrc,937
fontTools/ttLib/tables/_c_m_a_p.py,sha256=fXKv3N1G0i3LnW3hyQxLBtjNDyTmX09d5DCapHBwaw8,63299
fontTools/ttLib/tables/_c_v_a_r.py,sha256=5LCuL07YXkqcqkSQeW4F6Vlx5XYtkjIIyiYfQ88Ydw0,3621
fontTools/ttLib/tables/_c_v_t.py,sha256=N3VW0LbHHJtcUU8_TURT4U-OeVGMluJhM5mJn0LH3TA,1639
fontTools/ttLib/tables/_f_e_a_t.py,sha256=g-B57skN59uZa3dQ88gMVpY1RmQH92cCkttJq2T4TzY,484
fontTools/ttLib/tables/_f_p_g_m.py,sha256=MiDyOiSyn4iA92G8YDZytJMqTFgNix03oiX5xMhNkT0,1633
fontTools/ttLib/tables/_f_v_a_r.py,sha256=hpxU0-u7_pZYDmQdKpmd_7c6BjVpXHdoQ97jKk9Kz5U,9098
fontTools/ttLib/tables/_g_a_s_p.py,sha256=Pr4X2CEg3a_nYAZrKSWT0auQ5HU2WutP1Shxxg7ALPw,2266
fontTools/ttLib/tables/_g_c_i_d.py,sha256=diZlew4U8nFK5vimh-GMOjwHw8ccZtIEl9cPq0PrNdA,375
fontTools/ttLib/tables/_g_l_y_f.py,sha256=XbmhcVgnrla9Fyn5okzrMH3JvI1rra6Ezg6KZpZtYYA,86818
fontTools/ttLib/tables/_g_v_a_r.py,sha256=etECZEoWa1KEimNg2hqNFqPqr85OFsfj3odDIU-0lRI,11065
fontTools/ttLib/tables/_h_d_m_x.py,sha256=Da8Og1KFmOLJGkBlLDlcgdvrIeCTx1USGwnmlg93r3E,4398
fontTools/ttLib/tables/_h_e_a_d.py,sha256=ft9ghTA1NZsGBvB0yElFFCqVHecuCKGjT2m2GfYB3Yc,5056
fontTools/ttLib/tables/_h_h_e_a.py,sha256=pY92ZLt3o0jZ3KQVd_qtxYtk_tbP2DLzSWm_wVP8FNM,4914
fontTools/ttLib/tables/_h_m_t_x.py,sha256=p-9K-E3LcdJByagZ-0F0OA11pCVfNS9HtKRjbpvMM6I,6202
fontTools/ttLib/tables/_k_e_r_n.py,sha256=AjG5Fd6XaPAdXi5puDtLuMrfCsHUi9X7uFh76QGCMrc,11083
fontTools/ttLib/tables/_l_c_a_r.py,sha256=N-1I6OJHvnF_YfGktyfFTRAG5lrExV7q6HX-0ffSRyQ,403
fontTools/ttLib/tables/_l_o_c_a.py,sha256=d1pX-QByFSJ00K9NQaOGbAHDr0pax_48PkfCupvekkw,2264
fontTools/ttLib/tables/_l_t_a_g.py,sha256=rnf8P_C_RIb37HBNk0qDSxP7rK-N9j5CcQHgMrPSuxw,2624
fontTools/ttLib/tables/_m_a_x_p.py,sha256=9B6lvWo4y42dyLPIvG6CsVOlWCk7bs4DoVJDB8ViEew,5411
fontTools/ttLib/tables/_m_e_t_a.py,sha256=I8HaZgcIPQZcCxBiSX0rGrfrs-zXRGUfEbJ8eGvZ07A,4025
fontTools/ttLib/tables/_m_o_r_t.py,sha256=LU3D9PmV_nFs6hoccGmr1pfUzjJaeB_WRW2OIS0RwPc,501
fontTools/ttLib/tables/_m_o_r_x.py,sha256=vLyrtx_O__BwnPi7Qo3oT8WHaANRARtHcqHSdZ5ct0E,563
fontTools/ttLib/tables/_n_a_m_e.py,sha256=eMkpYbxSfXpXhzuo87b9sQ6-KRStAVCeoXi5jrwVyME,42671
fontTools/ttLib/tables/_o_p_b_d.py,sha256=lfJi6kblt_nGmGmRSupwEaud3Ri_y6ftWNuyrCPpzQ0,462
fontTools/ttLib/tables/_p_o_s_t.py,sha256=rgY2-OvgvCK_F6qcBUrPIEcIMpBvWfX1AJEOyxxOGqI,11927
fontTools/ttLib/tables/_p_r_e_p.py,sha256=qWDjHiHvHaJCx2hYFmjJeMwpgwvD-cG5zkibMh9TWuk,443
fontTools/ttLib/tables/_p_r_o_p.py,sha256=ux5Z0FrE7uuKQrO-SCQwButVtKmEAsvfDE6mOP_SOnE,439
fontTools/ttLib/tables/_s_b_i_x.py,sha256=KF9acCLqBcYpg92h5vJBp5LsNT7c4MDKD4rocixRPKw,4994
fontTools/ttLib/tables/_t_r_a_k.py,sha256=7PLK_3VaZxxdgdn4wiPbMLvmUl0JZIWLWqgl-wvVvvQ,11711
fontTools/ttLib/tables/_v_h_e_a.py,sha256=ay73lNwQR72zZeyQ00ejfds2XmUp7sOLidnSzMvawUw,4598
fontTools/ttLib/tables/_v_m_t_x.py,sha256=933DMeQTI9JFfJ3TOjAFE6G8qHXJ7ZI2GukIKSQjaFU,519
fontTools/ttLib/tables/asciiTable.py,sha256=xJtOWy5lATZJILItU-A0dK4-jNXBByzyVWeO81FW8nc,657
fontTools/ttLib/tables/grUtils.py,sha256=T_WsEtpW60m9X6Rulko3bGI9aFdSC8Iyffwg_9ky0_I,2362
fontTools/ttLib/tables/otBase.py,sha256=RaBfW20QPTYPWVDzAIjFGgYfZqxeiO6RnZbVs30wpRg,54820
fontTools/ttLib/tables/otConverters.py,sha256=n0oeHHlsnh203r23dKwQmwUMWeXKagd6cxAxpEdkG2g,76138
fontTools/ttLib/tables/otData.py,sha256=0xXwRw_ea_fDWY7GoPgGCjkI_j8s-IP3Tmj2YUWy7Cg,203660
fontTools/ttLib/tables/otTables.py,sha256=l-mkq4gRbRNnAdTh-TpBgj0lB6XqgsQVuw1FWCRXXxI,99637
fontTools/ttLib/tables/otTraverse.py,sha256=tG0g7nPncZ6_grWs55-ghEdBiiYuw6mtMBwYYVk4PVE,5659
fontTools/ttLib/tables/sbixGlyph.py,sha256=a-mCmO5EibN_He7QQohG06Qg-fCOHWiNFMAbCpxa25w,5945
fontTools/ttLib/tables/sbixStrike.py,sha256=9-UIVPormVd27lOU5fuGZvzkckA2U5975jBXXUEPxKA,6840
fontTools/ttLib/tables/table_API_readme.txt,sha256=E9lwGW1P_dGqy1FYBcYLVEDDmikbsqW4pUtpv1RKCJU,2839
fontTools/ttLib/tables/ttProgram.py,sha256=vkRtptH7QXD0Ng8LNzh-A_Ln27VPCxSJOXgW8878nSo,36482
fontTools/ttLib/ttCollection.py,sha256=1_wMr_ONgwPZh6wfbS_a7lNeE2IxUqd029TGObOsWs0,4088
fontTools/ttLib/ttFont.py,sha256=zyTL_lOCvJye5gzc1zvcqph2uEbR69AH79VclrLOftc,42131
fontTools/ttLib/ttGlyphSet.py,sha256=FvznrgEaD-Rz0737kfQGRFD6jX4uGpGqbu5ZCQtVuyI,18353
fontTools/ttLib/ttVisitor.py,sha256=_Dkmz0tDs-5AFUR46kyg3Ku6BMPifrZzRU8-9UvXdz4,1057
fontTools/ttLib/woff2.py,sha256=LuX5SHMlhuhNRI0W5J2UnDua7_3NCnGaoofXhmjkf3g,62741
fontTools/ttx.py,sha256=BnEtamf1FYKKbA7YimZY3BNhd1lQrQJzHu20dmlDySY,17119
fontTools/ufoLib/__init__.py,sha256=glPJqMzIifK8vSKRcq-hv7GI3DR4iGdEt0twxIEh2SA,96890
fontTools/ufoLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/converters.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/errors.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/etree.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/filenames.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/glifLib.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/kerning.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/plistlib.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/pointPen.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/utils.cpython-311.pyc,,
fontTools/ufoLib/__pycache__/validators.cpython-311.pyc,,
fontTools/ufoLib/converters.py,sha256=0qyvTmJ1usSxDhzoOKsOACh5TLpTLYbTKJnrz-8yxN4,10892
fontTools/ufoLib/errors.py,sha256=WvbkGMx7W0b8IE7S7LmfbyVTm-Xc0cRSKsXbJh0NEX0,606
fontTools/ufoLib/etree.py,sha256=r0qPJqXTnZuWr-8YsVgyLddHRasf3yX8DDants9hBq8,230
fontTools/ufoLib/filenames.py,sha256=UjLQYfYQqQz96zN6Xv4jAjGBsK0gUhidp8LvlEIt0aQ,7853
fontTools/ufoLib/glifLib.py,sha256=ZAAVhdZGP9QJQ_XmUmVfvTXDjtUx4qlz_WtQt5jdQlw,74388
fontTools/ufoLib/kerning.py,sha256=2DPHpQ9Mt46RTrUzzmVmBYFpCD5QBOGm9VSLtmNNdQA,3064
fontTools/ufoLib/plistlib.py,sha256=GpWReRtO7S1JCv6gJnnuiYooo4Hwbgc2vagT041kFk8,1557
fontTools/ufoLib/pointPen.py,sha256=bU0-DLHrWKyutmwjw0tvhT-QPE-kmqs2Dqe0cflYgOk,250
fontTools/ufoLib/utils.py,sha256=3Amg-lMg24NdCV-fkvninfD_XUBCKBNHabf3QGZYIqw,1969
fontTools/ufoLib/validators.py,sha256=nZgijPWW40jzcQb6YgujVFVi53mPPlmrQ1uU8QJT_Gs,31991
fontTools/unicode.py,sha256=a7460sU25TnVYGzrVl0uv0lI_pDbANZp8Jfmqx9tAag,1287
fontTools/unicodedata/Blocks.py,sha256=OjbkcPqRLDgLpJ81rBGOsCDtDpVzPgUEKGq56ZGOYGM,33218
fontTools/unicodedata/OTTags.py,sha256=IAt8NXaZOhu5cuuks46DDX3E7Ovoqp-PMUQC-WJUPIs,1246
fontTools/unicodedata/ScriptExtensions.py,sha256=jEz55Ky6pFvoItYPuEPtXFVEVGRlMYzcStv4OCW5saY,28519
fontTools/unicodedata/Scripts.py,sha256=8V1lWvAF4VL5Z_QM-LCu3XbWQSrhXCgfIUQUAHtwBBk,133890
fontTools/unicodedata/__init__.py,sha256=chzMtaCBGWRqh-2kzN3-DVPO1xAaWHa4Y0szHVAaLJs,9122
fontTools/unicodedata/__pycache__/Blocks.cpython-311.pyc,,
fontTools/unicodedata/__pycache__/OTTags.cpython-311.pyc,,
fontTools/unicodedata/__pycache__/ScriptExtensions.cpython-311.pyc,,
fontTools/unicodedata/__pycache__/Scripts.cpython-311.pyc,,
fontTools/unicodedata/__pycache__/__init__.cpython-311.pyc,,
fontTools/varLib/__init__.py,sha256=OL2M224kjlhjCJtI7z75qRx6fSH1kx6lcsrteP2r7H8,55494
fontTools/varLib/__main__.py,sha256=ykyZY5GG9IPDsPrUWiHgXEnsgKrQudZkneCTes6GUpU,101
fontTools/varLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/varLib/__pycache__/__main__.cpython-311.pyc,,
fontTools/varLib/__pycache__/avar.cpython-311.pyc,,
fontTools/varLib/__pycache__/avarPlanner.cpython-311.pyc,,
fontTools/varLib/__pycache__/builder.cpython-311.pyc,,
fontTools/varLib/__pycache__/cff.cpython-311.pyc,,
fontTools/varLib/__pycache__/errors.cpython-311.pyc,,
fontTools/varLib/__pycache__/featureVars.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatable.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatableHelpers.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatablePlot.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatableTestContourOrder.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolatableTestStartingPoint.cpython-311.pyc,,
fontTools/varLib/__pycache__/interpolate_layout.cpython-311.pyc,,
fontTools/varLib/__pycache__/iup.cpython-311.pyc,,
fontTools/varLib/__pycache__/merger.cpython-311.pyc,,
fontTools/varLib/__pycache__/models.cpython-311.pyc,,
fontTools/varLib/__pycache__/multiVarStore.cpython-311.pyc,,
fontTools/varLib/__pycache__/mutator.cpython-311.pyc,,
fontTools/varLib/__pycache__/mvar.cpython-311.pyc,,
fontTools/varLib/__pycache__/plot.cpython-311.pyc,,
fontTools/varLib/__pycache__/stat.cpython-311.pyc,,
fontTools/varLib/__pycache__/varStore.cpython-311.pyc,,
fontTools/varLib/avar.py,sha256=tRgKAUn_K5MTCSkB2MgPYYZ2U6Qo_Cg3jFQV0TDKFgc,9907
fontTools/varLib/avarPlanner.py,sha256=orjyFvg3YkC-slt7fgSEU1AGjLCkGgMEJ7hTRV6CqUA,28362
fontTools/varLib/builder.py,sha256=1k-N-rTwnZqQpzhNLBx2tqu2oYGG44sJSXKTCjAvIVM,6824
fontTools/varLib/cff.py,sha256=bl8rrPHHpwzUdZBY80_5JJLWYcXQOolhKKvTJiiU-Bs,23532
fontTools/varLib/errors.py,sha256=mXl-quT2Z75_t7Uwb6ug3VMhmbQjO841YNLeghwuY_s,7153
fontTools/varLib/featureVars.py,sha256=e7e4UFG3t5GaNLXxsA6mcn7Cs3L1UjwKzXoHPnamWJE,26137
fontTools/varLib/instancer/__init__.py,sha256=oWhXm_LpZbP29JT_aw2fLPeZadz5LaKSLBsNDv7bacA,73283
fontTools/varLib/instancer/__main__.py,sha256=YN_tyJDdmLlH3umiLDS2ue0Zc3fSFexa9wCuk3Wuod0,109
fontTools/varLib/instancer/__pycache__/__init__.cpython-311.pyc,,
fontTools/varLib/instancer/__pycache__/__main__.cpython-311.pyc,,
fontTools/varLib/instancer/__pycache__/featureVars.cpython-311.pyc,,
fontTools/varLib/instancer/__pycache__/names.cpython-311.pyc,,
fontTools/varLib/instancer/__pycache__/solver.cpython-311.pyc,,
fontTools/varLib/instancer/featureVars.py,sha256=b3qtGCYVZ9fqkqcgFQUikYQBX_3_x0YgdrvvxIALbuU,7300
fontTools/varLib/instancer/names.py,sha256=vmHi7JZlh-N4amxKdaTJ-5DN9mDJ8Wnh_s9W1gJAQ4Y,15338
fontTools/varLib/instancer/solver.py,sha256=7noVYZ6gHrv4tV7kaXHn4iMKs_YP2YNssr4zgCHk4qI,11311
fontTools/varLib/interpolatable.py,sha256=8AXrhsnYY1z0hR6gskqYRYx8qcFsvUKmIIHZRpIOlAU,46430
fontTools/varLib/interpolatableHelpers.py,sha256=JnabttZY7sY9-QzdiqkgzQ_S5nG8k_O1TzLEmfNUvNo,11892
fontTools/varLib/interpolatablePlot.py,sha256=tUKFd8H9B2eD_GE6jV13J-dZkkIeLmk3ojAYrf-edsA,45644
fontTools/varLib/interpolatableTestContourOrder.py,sha256=Pbt0jW0LoVggIwrtADZ7HWK6Ftdoo1bjuWz0ost0HD0,3103
fontTools/varLib/interpolatableTestStartingPoint.py,sha256=f5MJ3mj8MctJCvDJwqmW1fIVOgovUMYAOela9HweaRU,4403
fontTools/varLib/interpolate_layout.py,sha256=tTPUes_K7MwooUO_wac9AeFEVgL1uGSz4ITYiOizaME,3813
fontTools/varLib/iup.c,sha256=DLuKtbG8sRuBAZXN0blJBfPYEUjsuImoF1Rnnn_f-Os,776376
fontTools/varLib/iup.cp311-win_amd64.pyd,sha256=ON14fOwVlH3LNFlFIAHWn2DVUqKrrr_tnilcPA_1vws,136704
fontTools/varLib/iup.py,sha256=O_xPJOBECrNDbQqCC3e5xf9KsWXUd1i3BAp9Fl6Hv2Y,15474
fontTools/varLib/merger.py,sha256=V-B17poOYbbrRsfUYJbdqt46GtRfG833MKwtv9NOB3Q,62519
fontTools/varLib/models.py,sha256=ZqQb1Lapj5dCO8dwa3UTx1LsIpF0-GiDte32t_TMJJQ,23040
fontTools/varLib/multiVarStore.py,sha256=OvrrTaKrCZCXP40Rrv-2w416P-dNz3xE6gPOEyS3PrY,8558
fontTools/varLib/mutator.py,sha256=bUkUP27sxhEVkdljzbHNylHkj6Ob3FfQ9AoDYTRIwdo,19796
fontTools/varLib/mvar.py,sha256=Gf3q54ICH-E9oAwKYeIKUPLZabfjY0bUT4t220zLzYI,2489
fontTools/varLib/plot.py,sha256=BtozrcnKoEyCs0rGy7PZmrUvUNTmZT-5_sylW5PuJ28,7732
fontTools/varLib/stat.py,sha256=ScaVFIVpXTqA-F07umv_66GoxtcjaZ54MPLFvFK4s68,4960
fontTools/varLib/varStore.py,sha256=lWaQpZ7xc4Gh00chbRi9aZh8gdtTS8Jizcd-WSHt9NI,25927
fontTools/voltLib/__init__.py,sha256=J7W0S2YED0GOqW9B_ZOhw-oL0-ltuRDYgAbrd8XHjqA,156
fontTools/voltLib/__pycache__/__init__.cpython-311.pyc,,
fontTools/voltLib/__pycache__/ast.cpython-311.pyc,,
fontTools/voltLib/__pycache__/error.cpython-311.pyc,,
fontTools/voltLib/__pycache__/lexer.cpython-311.pyc,,
fontTools/voltLib/__pycache__/parser.cpython-311.pyc,,
fontTools/voltLib/__pycache__/voltToFea.cpython-311.pyc,,
fontTools/voltLib/ast.py,sha256=pGv6HjsHl_YFKLD4Btvu3KpYFOP_PwE4wLQbzxNZuhg,13674
fontTools/voltLib/error.py,sha256=3TsaZBA82acFd2j5Beq3WUQTURTKM0zxOnUFGZovSNA,407
fontTools/voltLib/lexer.py,sha256=v9V4zdBO2VqVJG__IWrL8fv_CRURmh2eD_1UpbIJn9g,3467
fontTools/voltLib/parser.py,sha256=xAQPwO8uyYHKi-v9yMtz2RGE3lj75V4lanUGgIL9AC4,25572
fontTools/voltLib/voltToFea.py,sha256=iJ7mJa9q-vWpmQgfCJC_ZrzNop_mgC1pazr1DhPJleE,29235
fonttools-4.55.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fonttools-4.55.3.dist-info/LICENSE,sha256=Ir74Bpfs-qF_l-YrmibfoSggvgVYPo3RKtFpskEnTJk,1093
fonttools-4.55.3.dist-info/METADATA,sha256=XVEqqqeWctr-6KmXfMHa2x5rdO-01XnMOD-OeK_nOdI,168502
fonttools-4.55.3.dist-info/RECORD,,
fonttools-4.55.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fonttools-4.55.3.dist-info/WHEEL,sha256=nkBcd8Ko0v5sEcSagm2-x_RVrb8gBSkTa8VFFZ0Mr1o,101
fonttools-4.55.3.dist-info/entry_points.txt,sha256=8kVHddxfFWA44FSD4mBpmC-4uCynQnkoz_9aNJb227Y,147
fonttools-4.55.3.dist-info/top_level.txt,sha256=rRgRylrXzekqWOsrhygzib12pQ7WILf7UGjqEwkIFDM,10
