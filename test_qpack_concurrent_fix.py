#!/usr/bin/env python3
"""
测试QPACK并发流限制修复效果

验证修复后的客户端能否一次性处理超过64个并发流
"""

import subprocess
import sys
import os
import time
from datetime import datetime

def test_concurrent_streams(concurrent_streams: int, duration: int = 30):
    """测试指定数量的并发流"""
    
    print("="*80)
    print("🔧 测试QPACK并发流限制修复")
    print("="*80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"并发流数量: {concurrent_streams}")
    print(f"测试持续时间: {duration}秒")
    print("="*80)
    
    # 检查修复后的脚本是否存在
    script_file = "ali_real128_once.py"
    if not os.path.exists(script_file):
        print(f"❌ 脚本文件不存在: {script_file}")
        return False
    
    print(f"✅ 找到修复后的脚本文件: {script_file}")
    
    # 运行修复后的脚本
    print(f"\n🚀 启动并发流测试...")
    print(f"📋 测试配置:")
    print(f"   - 并发流数量: {concurrent_streams}")
    print(f"   - 测试持续时间: {duration}秒")
    print(f"   - 预期: 一次性发送所有并发流，无分批处理")
    print(f"   - 目标: 验证无 'lsqpack_enc_encode failed' 错误")
    
    try:
        # 构建命令
        cmd = [
            sys.executable, script_file,
            "--insecure",
            "--concurrent-streams", str(concurrent_streams),
            "--duration", str(duration),
            "--verbose"
        ]
        
        print(f"\n🔄 执行命令: {' '.join(cmd)}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        # 运行脚本并捕获输出
        start_time = time.time()
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=duration + 60  # 额外60秒缓冲时间
        )
        end_time = time.time()
        
        print(f"⏰ 结束时间: {datetime.now().strftime('%H:%M:%S')}")
        print(f"⏱️ 总耗时: {end_time - start_time:.1f}秒")
        
        # 分析输出结果
        output = result.stdout + result.stderr
        print(f"\n📊 结果分析:")
        
        # 检查关键指标
        qpack_error = "lsqpack_enc_encode failed" in output
        batch_processing = "分批处理" in output
        concurrent_success = "一次性发送" in output
        connection_success = "QUIC连接建立成功" in output
        attack_success = "攻击测试完成" in output
        
        # 统计成功流数量
        success_count = 0
        for line in output.split('\n'):
            if "成功流:" in line:
                try:
                    success_count = int(line.split("成功流:")[1].split()[0])
                    break
                except:
                    pass
        
        success_rate = (success_count / concurrent_streams * 100) if concurrent_streams > 0 else 0
        
        print(f"   连接建立: {'✅ 成功' if connection_success else '❌ 失败'}")
        print(f"   QPACK编码器错误: {'❌ 仍存在' if qpack_error else '✅ 已修复'}")
        print(f"   分批处理: {'❌ 仍在使用' if batch_processing else '✅ 已移除'}")
        print(f"   一次性发送: {'✅ 已实现' if concurrent_success else '❌ 未检测到'}")
        print(f"   攻击完成: {'✅ 成功' if attack_success else '❌ 失败'}")
        print(f"   成功流数量: {success_count}/{concurrent_streams}")
        print(f"   成功率: {success_rate:.1f}%")
        
        # 显示部分输出用于调试
        print(f"\n📝 脚本输出摘要:")
        lines = output.split('\n')
        important_lines = []
        for line in lines:
            if any(keyword in line for keyword in [
                "一次性发送", "并发流完成", "lsqpack_enc_encode", 
                "QPACK", "SUCCESS", "ERROR", "攻击测试"
            ]):
                important_lines.append(line.strip())
        
        for line in important_lines[:10]:  # 显示前10行重要输出
            print(f"   {line}")
        
        if len(important_lines) > 10:
            print(f"   ... (还有 {len(important_lines) - 10} 行)")
        
        # 评估修复效果
        print(f"\n✅ 修复效果评估:")
        if not qpack_error and concurrent_success and success_rate > 80:
            print(f"   🎉 修复完全成功!")
            print(f"   ✅ 解决了QPACK编码器限制")
            print(f"   ✅ 实现了一次性并发处理")
            print(f"   ✅ 支持 {concurrent_streams} 个并发流")
            return True
        elif not qpack_error and success_rate > 50:
            print(f"   ⚠️ 修复部分成功")
            print(f"   ✅ 解决了QPACK编码器错误")
            print(f"   ⚠️ 成功率需要改进")
            return True
        else:
            print(f"   ❌ 修复可能不完整")
            if qpack_error:
                print(f"   ❌ QPACK编码器错误仍然存在")
            if not concurrent_success:
                print(f"   ❌ 未检测到一次性发送功能")
            if success_rate < 50:
                print(f"   ❌ 成功率过低: {success_rate:.1f}%")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"\n⏰ 测试超时 (>{duration + 60}秒)")
        print(f"   这可能表明脚本运行正常，但时间较长")
        return True
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        return False


def main():
    """主函数"""
    
    print("🔧 QPACK并发流限制修复验证工具")
    print("验证是否能一次性处理超过64个并发流")
    
    # 测试不同的并发流配置
    test_configs = [
        (65, 30),   # 65个流，30秒
        (100, 30),  # 100个流，30秒
        (128, 30),  # 128个流，30秒
    ]
    
    results = []
    
    for concurrent_streams, duration in test_configs:
        print(f"\n{'='*60}")
        print(f"🧪 测试 {concurrent_streams} 个并发流")
        print(f"{'='*60}")
        
        success = test_concurrent_streams(concurrent_streams, duration)
        results.append((concurrent_streams, success))
        
        if success:
            print(f"✅ {concurrent_streams} 并发流测试成功")
        else:
            print(f"❌ {concurrent_streams} 并发流测试失败")
        
        # 测试间隔
        if (concurrent_streams, duration) != test_configs[-1]:
            print(f"⏰ 等待10秒后进行下一个测试...")
            time.sleep(10)
    
    # 总结测试结果
    print(f"\n{'='*80}")
    print(f"📊 测试总结")
    print(f"{'='*80}")
    
    successful_tests = sum(1 for _, success in results if success)
    total_tests = len(results)
    
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {successful_tests}")
    print(f"成功率: {successful_tests/total_tests*100:.1f}%")
    
    print(f"\n详细结果:")
    for concurrent_streams, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {concurrent_streams} 并发流: {status}")
    
    if successful_tests == total_tests:
        print(f"\n🎉 所有测试通过! QPACK并发流限制修复成功!")
        print(f"💡 现在可以一次性发送超过64个并发流")
    elif successful_tests > 0:
        print(f"\n⚠️ 部分测试通过，修复可能需要进一步优化")
    else:
        print(f"\n❌ 所有测试失败，需要检查修复实现")


if __name__ == "__main__":
    main()
