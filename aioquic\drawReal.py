import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# --- 1. 配置参数 ---
EXCEL_FILE_PATH = r"D:\Work__\Pycharm_Work\http3_python\aioquic\amplification_ratios_detailed2.xlsx"

# 【重要】根据 Excel 中的实际列名修改这里
X_COLUMN = 'Concurrent Streams'  # A列: 并发流数量
SERVER_MBPS_COLUMN = '实际源站mbs'  # B列: 源站带宽 (Mbps)
LOCAL_BPS_COLUMN = '实际本地(bytes/s)'  # C列: 本地带宽 (bytes/s)


# --- 2. 读取并处理数据 ---
def load_and_process_data(filepath):
    """从Excel加载数据并进行必要的计算和清理"""
    try:
        # 使用 pandas 读取 Excel 文件
        df = pd.read_excel(filepath, engine='openpyxl')
        print("✅ Excel 文件读取成功！")

        # --- 数据清理和转换 ---
        # 确保数据列是数值类型，对于无法转换的错误值，将其设为 NaN (Not a Number)
        df[X_COLUMN] = pd.to_numeric(df[X_COLUMN], errors='coerce')
        df[SERVER_MBPS_COLUMN] = pd.to_numeric(df[SERVER_MBPS_COLUMN], errors='coerce')
        df[LOCAL_BPS_COLUMN] = pd.to_numeric(df[LOCAL_BPS_COLUMN], errors='coerce')

        # 删除包含任何 NaN 值的行，以防有空行或错误数据
        df.dropna(subset=[X_COLUMN, SERVER_MBPS_COLUMN, LOCAL_BPS_COLUMN], inplace=True)

        # --- 计算放大倍数 ---
        # 1. 将本地带宽从 Bytes/s 转换为 Mbps
        #    公式: (Bytes/s * 8) / 1,000,000 = Mbps
        df['Local Mbps'] = (df[LOCAL_BPS_COLUMN] * 8) / 1_000_000

        # 2. 计算放大倍数 = 服务器带宽 / 本地带宽
        #    为避免除以零的错误，我们将本地带宽为0的地方结果设为0
        df['Amplification Ratio'] = df.apply(
            lambda row: row[SERVER_MBPS_COLUMN] / row['Local Mbps'] if row['Local Mbps'] > 0 else 0,
            axis=1
        )

        print("📊 数据处理和放大倍数计算完成！")
        # 打印数据的前几行以供检查
        print("数据预览:")
        print(df.head())

        return df

    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 '{filepath}'。请检查路径是否正确。")
        return None
    except Exception as e:
        print(f"❌ 读取或处理文件时出错: {e}")
        return None


# --- 3. 绘图 ---
def plot_amplification_ratio(df):
    """使用处理后的数据绘制放大效果图"""
    if df is None or df.empty:
        print("没有可用于绘图的数据。")
        return

    print("🎨 开始绘图...")

    # 设置图形风格，模仿参考图
    try:
        plt.style.use('seaborn-v0_8-whitegrid')  # 使用带网格的风格
    except:
        plt.style.use('ggplot')  # 备用风格

    fig, ax = plt.subplots(figsize=(12, 7))
    # fig.set_facecolor('#FBF9F4') # 如果喜欢米黄色背景可以取消注释
    # ax.set_facecolor('#FBF9F4')

    # 绘制主曲线
    ax.plot(
        df[X_COLUMN],
        df['Amplification Ratio'],
        label='Alibaba (Actual Test)',
        color='#4A69BD',  # 使用蓝色
        linewidth=2
    )

    # 添加垂直虚线，可以根据需要调整位置
    ax.axvline(x=125, color='gray', linestyle=':', linewidth=1.5, alpha=0.6)
    ax.axvline(x=250, color='gray', linestyle=':', linewidth=1.5, alpha=0.6)

    # 设置标题和标签
    ax.set_title('Actual Bandwidth Amplification Ratio (Alibaba)', fontsize=18, pad=20)
    ax.set_xlabel('Number of Concurrent Streams', fontsize=14)
    ax.set_ylabel('Bandwidth Amplification Ratio', fontsize=14)

    # 设置坐标轴范围 (可以根据您的数据进行微调)
    # x轴从0到数据最大值+10，y轴从0到放大倍数最大值+10
    ax.set_xlim(0, df[X_COLUMN].max() + 10)
    ax.set_ylim(0, df['Amplification Ratio'].max() + 10)

    # 添加图例
    ax.legend(loc='upper left', fontsize=12)

    # 优化布局并显示图形
    plt.tight_layout()
    plt.show()
    print("✅ 图形显示完成！")


# --- 主程序入口 ---
if __name__ == "__main__":
    # 1. 加载和处理数据
    data_df = load_and_process_data(EXCEL_FILE_PATH)

    # 2. 绘制图形
    plot_amplification_ratio(data_df)