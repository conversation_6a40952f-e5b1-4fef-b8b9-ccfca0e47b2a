from typing import Tuple

class AEAD:
    def __init__(self, cipher_name: bytes, key: bytes, iv: bytes): ...
    def decrypt(
        self, data: bytes, associated_data: bytes, packet_number: int
    ) -> bytes: ...
    def encrypt(
        self, data: bytes, associated_data: bytes, packet_number: int
    ) -> bytes: ...

class CryptoError(ValueError): ...

class HeaderProtection:
    def __init__(self, cipher_name: bytes, key: bytes): ...
    def apply(self, plain_header: bytes, protected_payload: bytes) -> bytes: ...
    def remove(self, packet: bytes, encrypted_offset: int) -> Tuple[bytes, int]: ...
