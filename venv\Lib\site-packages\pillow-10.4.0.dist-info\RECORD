PIL/BdfFontFile.py,sha256=JJLBb0JZwTmSIIkqQoe2vzus-XTczN_O47DQneXKM1o,3610
PIL/BlpImagePlugin.py,sha256=n7Eghktvwb6WVWouvrLMGzB66PiP6aPZaKwOzx_5ux0,16854
PIL/BmpImagePlugin.py,sha256=CbsPOXskQNx7ZoIlloRNntrmBo0UkYkwlWrvwEhCSks,19054
PIL/BufrStubImagePlugin.py,sha256=sY28XJU_Fu-UsbPpAoN-fN63FemmhCMi8rW5Kf9JioE,1829
PIL/ContainerIO.py,sha256=BTz6Qlz-VyDmurXnWpQU-lAevLxgcsOcEGZP0CtvSKc,3302
PIL/CurImagePlugin.py,sha256=3e6_djFaRvGO2PMP_E0HwbHx4SjqTlmlvPraNUFeLkQ,1839
PIL/DcxImagePlugin.py,sha256=iaVs9updbtEstQKPLKKIlJVhfxFarbgCPoO8j96BmDA,2114
PIL/DdsImagePlugin.py,sha256=dwlTfJcpUA9NAcWW6WoRjsexu_5xFTDYJhYuQnSLUJ4,17489
PIL/EpsImagePlugin.py,sha256=I7DstQ6ZijJMglp6qEi1xljwY2bY4MkwWPWs0WPWM4Y,16614
PIL/ExifTags.py,sha256=LA3OxhNImajSkIRnCMXDTJw4MprMEeFq_Sqf-sjn20w,10134
PIL/FitsImagePlugin.py,sha256=ngEk16Ljz2K1f-Jz-KConyALGxVzFcVTEpWW1-VjVgw,4745
PIL/FliImagePlugin.py,sha256=l8awoi3gN9gWsBonvjwDY-StXLSGnN6h5ZZg1S09Pjs,4785
PIL/FontFile.py,sha256=iLSV32yQetLnE4SgG8HnHb2FdqkqFBjY9n--E6u5UE0,3711
PIL/FpxImagePlugin.py,sha256=uecjqTuKyZHle3cg2VYpCthkIlfzVFnvDKBI3T6EdrA,7315
PIL/FtexImagePlugin.py,sha256=KQwb4dvvnBxE4WOiuAjdougODgm67AsbYGWapChmDwU,3617
PIL/GbrImagePlugin.py,sha256=KbLlo2oVhIbkzP7zQW0vARTE6aMjX6DpJEkghUny6Gk,3071
PIL/GdImageFile.py,sha256=5SZA0952NckwJYsproC-ykj_UfVUjiGxNiohzRYf2fE,2897
PIL/GifImagePlugin.py,sha256=eubRq9j9MVgEbJt0A4ARRBdIIoFjAkh5dheNJ0J27IA,40904
PIL/GimpGradientFile.py,sha256=AFEEGWtylUg7tIYK0MgBC4hZHq3WxSzIvdq_MAEAUq8,4047
PIL/GimpPaletteFile.py,sha256=EmKLnuvsHx0GLhWh8YnfidiTEhUm51-ZNKgQbAX1zcU,1485
PIL/GribStubImagePlugin.py,sha256=Vf_VvZltyP3QqTuz-gUfCT6I2g3F5Rh8BYMGjxwpAoM,1823
PIL/Hdf5StubImagePlugin.py,sha256=70vlB50QgPYYH2b8kE8U_QN5Q8TlmjmN8vk0FrhLkJ4,1826
PIL/IcnsImagePlugin.py,sha256=U8sXlLVueqXHOVfzKS9ELsIZeH0cE5miFKMjAagK5fI,12405
PIL/IcoImagePlugin.py,sha256=4OLIfifidVCQbA7VJpxH-VD-Ar0mzhIj8bFYP5rRO0o,12147
PIL/ImImagePlugin.py,sha256=uI_xoDFxGetvSkcgER5IHtmebcQcQXV_1wRu4ztOS-A,11494
PIL/Image.py,sha256=_J1nF9SELAzg188weTM0ozhnHj2WOPHJqXpyu56w5Ks,146669
PIL/ImageChops.py,sha256=hZ8EPUPlQIzugsEedV8trkKX0jBCDGb6Cszma6ZeMZQ,8257
PIL/ImageCms.py,sha256=W4juhbdt9eV_Zvf8WNO0oede2iCvUPZMtuFYy4cx9lA,43151
PIL/ImageColor.py,sha256=KV-u7HnZWrrL3zuBAOLqerI-7vFcXTxdLeoaYVjsnwI,9761
PIL/ImageDraw.py,sha256=QESkqodXPEg2Ao5-ZabcMkXLButss7h671RQPHV32ME,42528
PIL/ImageDraw2.py,sha256=aRmS6VPTFa4vpnHMLb3oOowjYt1W8ij81x5HOi5kotE,6229
PIL/ImageEnhance.py,sha256=xwj3y_MBWUJICjYc_l_8uIe7ZEtqUDyl9kQ0HQSQMTQ,3490
PIL/ImageFile.py,sha256=HiDum7mPIxlp_bB80wIw8mhYbmatDhMQYysI44rHrNQ,25847
PIL/ImageFilter.py,sha256=b-4kwg9GDMep01eRgwLlkWehHDioRHB-V3-pzpOZJ7g,19274
PIL/ImageFont.py,sha256=5TWH1kzlMp0Ru5hsIEZQXoMOHLyQvvkQD_cFPwObQHc,63357
PIL/ImageGrab.py,sha256=CJP_aZNA1mXU5dI77eElm4_Au198Uf7yVZ7Xw0BJ53s,6552
PIL/ImageMath.py,sha256=Ib655EKVOMeVJ2x_yVt0xaM2D9kfpCv2dX9CsCwizCM,11835
PIL/ImageMode.py,sha256=n4-2kSolyB7v2u6dXyIf3_vDL_LMvSNwhJvd9Do8cc8,2773
PIL/ImageMorph.py,sha256=5hHeZAmow6sFHRQ9wocxVcS4CmYcOCl5KUkPYxhOb9g,8820
PIL/ImageOps.py,sha256=RbcKwMwRLAv_UBwkmYfHRyv5aEKlcbTGsWMqcCgM0ek,25740
PIL/ImagePalette.py,sha256=jpyVO1j7nty9pUiB2f3i0Ds6U9ED6rwTuGWwaYAQRzk,9254
PIL/ImagePath.py,sha256=ZnnJuvQNtbKRhCmr61TEmEh1vVV5_90WMEPL8Opy5l8,391
PIL/ImageQt.py,sha256=tAl3NtwgTofzQnUc6F9TUQRD2u2HQOeRDqG-PadVzMg,6111
PIL/ImageSequence.py,sha256=jyVU7FmpmrvkBASsZIN-fI21g9DUcCzmSmj0FxS4Fng,2278
PIL/ImageShow.py,sha256=m-XAcuWPFoz8Dv_JfaqrI5ZEvNIxMbDKa4Po5PcAZ9A,10391
PIL/ImageStat.py,sha256=iA5KJrQeEpbwk-FhczGD8L4TaLWUt4VT_mp4drvMhv8,5485
PIL/ImageTk.py,sha256=rRGvldzlOvqgiseGvMH-jgiMmY5wri4SJzkvXOb9uJ4,8893
PIL/ImageTransform.py,sha256=xvHSE9-TtXtm_MilxVVf4dvB2-r7fJHhUFMJHFIs2PY,3994
PIL/ImageWin.py,sha256=jz_6kBZWKyJsUhYKvdMsQUsCn7ZJZPIYqaZOBE3mD1E,7761
PIL/ImtImagePlugin.py,sha256=TFLgRU0ko5gAxrsKUqGkK_Y8o233OVKLNJKE4IeOac8,2761
PIL/IptcImagePlugin.py,sha256=I2H6QXywjOeGpDo-LOEgJ7vYhhjbGEJgFjN6bcY0sSU,6370
PIL/Jpeg2KImagePlugin.py,sha256=P-2gvXFhjlDlKaWul8MXvQgQ19XzyeFHVh1fR_MgeLM,12809
PIL/JpegImagePlugin.py,sha256=5wcd77uSne-bEFHqPW6psgXnKw7spmR3gjZh_LYdvbI,30720
PIL/JpegPresets.py,sha256=0XoRcIdU_U1szfxfQEt_YgarmdB1INfTpbEgCanBbns,12664
PIL/McIdasImagePlugin.py,sha256=KNmMyMzyaBz_pjUhtxi0Fjtj6MdjzrT-b1P_LgC10gg,1979
PIL/MicImagePlugin.py,sha256=1mCwO0p5CJi_CXl3jFumdMxwd6vL11y0ckGjLayqPH8,2774
PIL/MpegImagePlugin.py,sha256=SR-JGne4xNIrHTc1vKdEbuW61oI-TIar2oIP-MeRuiI,2188
PIL/MpoImagePlugin.py,sha256=8jfsGFWqSx8D_ao9tPavMi45EWN7r080WnlZzyP7_2M,6027
PIL/MspImagePlugin.py,sha256=widjELAylDoYtS2Dc-JPePA7AiBo0uakagOptWkPD20,6036
PIL/PSDraw.py,sha256=L7W05HCqBKtZ271aEIPUheBzf0ZWcDgsXCvJUSMw3zc,7220
PIL/PaletteFile.py,sha256=lNPfuBTHraW6i1v1b9thNoFyIG2MRMMzFHxVTaxcwj8,1265
PIL/PalmImagePlugin.py,sha256=pxIMXUSRmuKUfbseT1K5dv7IqZTg_z5bphqC5PayF8E,9513
PIL/PcdImagePlugin.py,sha256=F_8URHMLJxGt_MhONZzako6F3qYcC6RkUZrKgr0FjTM,1689
PIL/PcfFontFile.py,sha256=RkM5wUp3SgRpQhpsTBEtk8uuFrQPnBSYBryOmcoRphQ,7401
PIL/PcxImagePlugin.py,sha256=Rq_7JaTH7MqWfC8ZxBExYAPCAeJwOaZwh7Iarg1TGfM,6444
PIL/PdfImagePlugin.py,sha256=Rk1HmJE0I2exdOuhK8nBe5CXaLkmllAeSnjajdXUq5c,9220
PIL/PdfParser.py,sha256=89DQp0TI7XA3EXw0mWrJADD_9ySWb8yldRRWN4i2SwY,35872
PIL/PixarImagePlugin.py,sha256=90zIgbzb8-bACCrJtQD2ubQmp_x5jGBOoWpjsS7Y038,1818
PIL/PngImagePlugin.py,sha256=4NibeAP8zvqFQzPvNsP6stiHUXo5VnIFq5I0LRt6MCQ,50055
PIL/PpmImagePlugin.py,sha256=bOOvSHAZHQ35WBSEYOW7YBFuIAkJuK2l5k5Geon_nF4,12587
PIL/PsdImagePlugin.py,sha256=bfF5u2gW4shYgSmIhAx8Qyn03YaYOY8KoQtFtSeEWA0,8615
PIL/PyAccess.py,sha256=vowudnRMgj8F9JvwHUsRzDV_szOtNommpH6SFH_bbzE,11224
PIL/QoiImagePlugin.py,sha256=vm98aN1PTbNv192gN6onYmbgSgT5f0V_AY4fr3VLySw,4261
PIL/SgiImagePlugin.py,sha256=RU265Sg7bEZN-TmU0M5E6ODfc4twjORx9oAD2fDsDQI,6683
PIL/SpiderImagePlugin.py,sha256=aCERP7JRSeUDuzLfm9jnhxVNKEyehM_p-1xSLZLDG5o,10379
PIL/SunImagePlugin.py,sha256=JT8IrJC7JIDBgdfIBzC-HSzxnoD4-W1aybpBDW0L0aU,4640
PIL/TarIO.py,sha256=LzQZ1ZRCVxXQREFRodRn9J_d25zxnSklBKdVikjzMGw,1651
PIL/TgaImagePlugin.py,sha256=Y48o5LSAAnNqthiC8nIBvOxyhPRemQfSZHKJMfiowT0,7204
PIL/TiffImagePlugin.py,sha256=w1drQBgBaCo3ElhzWesV9TXrZHw9x8KiuKq6XE4d5hA,80428
PIL/TiffTags.py,sha256=3sFYZ3VTzJcnCguPr_AIPYm7kT_sSdwqq5h10EKy0Ow,17270
PIL/WalImageFile.py,sha256=emYeZjisXBBE_RhK-hh6XVuC9N94pua3mwVXKwQ3rYw,5687
PIL/WebPImagePlugin.py,sha256=uHUch0U7vVAuCB9QSCHkPc6fZfdZRVdKe1vd8nbIsYY,11891
PIL/WmfImagePlugin.py,sha256=Lj7genL51Fu3YXcSce0xmaWNiKwzB3NUsCa-iMrvs0Q,5164
PIL/XVThumbImagePlugin.py,sha256=nlqdy2bGDbqG9Sl-62YQcfN0x8JAUgUQDo5a3CrNPQU,2162
PIL/XbmImagePlugin.py,sha256=g7Q5A_2yV4U293ygm_1SoIbYHnie3vajkMbqHrPgR6A,2747
PIL/XpmImagePlugin.py,sha256=1o0NGFfUTTvR7m8qDx1MAPeL22sJceEo5cdglBJzQYc,3344
PIL/__init__.py,sha256=98abxVfn8od1jJaTIr65YrYrIb7zMKbOJ5o68ryE2O0,2094
PIL/__main__.py,sha256=X8eIpGlmHfnp7zazp5mdav228Itcf2lkiMP0tLU6X9c,140
PIL/__pycache__/BdfFontFile.cpython-311.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-311.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-311.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-311.pyc,,
PIL/__pycache__/ContainerIO.cpython-311.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-311.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-311.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-311.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-311.pyc,,
PIL/__pycache__/ExifTags.cpython-311.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-311.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-311.pyc,,
PIL/__pycache__/FontFile.cpython-311.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-311.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-311.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-311.pyc,,
PIL/__pycache__/GdImageFile.cpython-311.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-311.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-311.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-311.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-311.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-311.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-311.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-311.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-311.pyc,,
PIL/__pycache__/Image.cpython-311.pyc,,
PIL/__pycache__/ImageChops.cpython-311.pyc,,
PIL/__pycache__/ImageCms.cpython-311.pyc,,
PIL/__pycache__/ImageColor.cpython-311.pyc,,
PIL/__pycache__/ImageDraw.cpython-311.pyc,,
PIL/__pycache__/ImageDraw2.cpython-311.pyc,,
PIL/__pycache__/ImageEnhance.cpython-311.pyc,,
PIL/__pycache__/ImageFile.cpython-311.pyc,,
PIL/__pycache__/ImageFilter.cpython-311.pyc,,
PIL/__pycache__/ImageFont.cpython-311.pyc,,
PIL/__pycache__/ImageGrab.cpython-311.pyc,,
PIL/__pycache__/ImageMath.cpython-311.pyc,,
PIL/__pycache__/ImageMode.cpython-311.pyc,,
PIL/__pycache__/ImageMorph.cpython-311.pyc,,
PIL/__pycache__/ImageOps.cpython-311.pyc,,
PIL/__pycache__/ImagePalette.cpython-311.pyc,,
PIL/__pycache__/ImagePath.cpython-311.pyc,,
PIL/__pycache__/ImageQt.cpython-311.pyc,,
PIL/__pycache__/ImageSequence.cpython-311.pyc,,
PIL/__pycache__/ImageShow.cpython-311.pyc,,
PIL/__pycache__/ImageStat.cpython-311.pyc,,
PIL/__pycache__/ImageTk.cpython-311.pyc,,
PIL/__pycache__/ImageTransform.cpython-311.pyc,,
PIL/__pycache__/ImageWin.cpython-311.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-311.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-311.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-311.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-311.pyc,,
PIL/__pycache__/JpegPresets.cpython-311.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-311.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-311.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-311.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-311.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PSDraw.cpython-311.pyc,,
PIL/__pycache__/PaletteFile.cpython-311.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PcfFontFile.cpython-311.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PdfParser.cpython-311.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PyAccess.cpython-311.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-311.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-311.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-311.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-311.pyc,,
PIL/__pycache__/TarIO.cpython-311.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-311.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-311.pyc,,
PIL/__pycache__/TiffTags.cpython-311.pyc,,
PIL/__pycache__/WalImageFile.cpython-311.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-311.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-311.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-311.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-311.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-311.pyc,,
PIL/__pycache__/__init__.cpython-311.pyc,,
PIL/__pycache__/__main__.cpython-311.pyc,,
PIL/__pycache__/_binary.cpython-311.pyc,,
PIL/__pycache__/_deprecate.cpython-311.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-311.pyc,,
PIL/__pycache__/_typing.cpython-311.pyc,,
PIL/__pycache__/_util.cpython-311.pyc,,
PIL/__pycache__/_version.cpython-311.pyc,,
PIL/__pycache__/features.cpython-311.pyc,,
PIL/__pycache__/report.cpython-311.pyc,,
PIL/_binary.py,sha256=cb9p-_mwzBYumlVsWbnoTWsrLo59towA6atLOZvjO3w,2662
PIL/_deprecate.py,sha256=5WrrZE3Q65nRF3pwwRN9wsmY4lqFOJayT6Uxt-i9tf0,2071
PIL/_imaging.cp311-win_amd64.pyd,sha256=6q0XE9vK5hkbkloSzkBl3iR6u8Brix5rNgk7ONhwOyk,2341888
PIL/_imaging.pyi,sha256=wjkxIX-PkUYnjQCdcvR3wcrZG9KEUL5m_L33Zw4W79A,846
PIL/_imagingcms.cp311-win_amd64.pyd,sha256=CPLczH_cSbZB_SNL0TxvUjfSNqt6OzyiazDNUBIVSrc,263168
PIL/_imagingcms.pyi,sha256=-1QgyLqhW56OLsu8Kgn9wc8IifLldpOuCRILf8SBfsE,4480
PIL/_imagingft.cp311-win_amd64.pyd,sha256=1tc9j4xR0XiE7pcvOcuzM8Pr91t2k1Ow5G66z5s6Jgk,1819136
PIL/_imagingft.pyi,sha256=SpEugAoNqOCdmR-bAghPf0AWfBpMfziUnXkJ65jY4dc,1748
PIL/_imagingmath.cp311-win_amd64.pyd,sha256=4_F5uzUxBrdP0yWVlfLS07sSB-STHaWU7U33LUrJgZY,24064
PIL/_imagingmath.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingmorph.cp311-win_amd64.pyd,sha256=CH-VT7c64JTsqELED6Ygz3V5Tqcy-NHpT1bCA9RzrTs,13824
PIL/_imagingmorph.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingtk.cp311-win_amd64.pyd,sha256=jMRx8vFn5e2hOYNz7rfGaJQg2jCNUHcXRave1z7ufu8,14848
PIL/_tkinter_finder.py,sha256=jKydPAxnrytggsZQHB6kAQep6A9kzRNyx_nToT4ClKY,561
PIL/_typing.py,sha256=ZEXNlEU-TV_Dl1RPO7Nx74CQQbrI6BGP-cDnRKWIrRQ,890
PIL/_util.py,sha256=ifUUlojtqTnWOxQFrwNCpqO1gjzkFIWovj7uBnq6DrY,844
PIL/_version.py,sha256=h4GziQDuM-Mtj--2-J-vf3al0vVtouQhTpAQOKexxWA,91
PIL/_webp.cp311-win_amd64.pyd,sha256=Wq2Zp0-MN4VcmJSOL1SVoHdQiRy5ByZ_s0YgNz4mVcQ,412672
PIL/_webp.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/features.py,sha256=UD4iRB2Hs4mR0odVOLJ1aLz_5YbGsBkL01-RO3oyrDk,10853
PIL/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
PIL/report.py,sha256=6m7NOv1a24577ZiJoxX89ip5JeOgf2O1F95f6-1K5aM,105
pillow-10.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pillow-10.4.0.dist-info/LICENSE,sha256=PFOBLi0mO1BbpKT2CpPZY3wmU-ISYDhlFccrXMtYqJ4,56937
pillow-10.4.0.dist-info/METADATA,sha256=EfTKW2FZO1afRRUC0aehHiVIUjsDtNAOIqNbPn_0Bac,9342
pillow-10.4.0.dist-info/RECORD,,
pillow-10.4.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pillow-10.4.0.dist-info/WHEEL,sha256=54Kd8K4FBe7Sg7uZbXfR37QJLsEQw5ItkM-7MynRyNc,101
pillow-10.4.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
pillow-10.4.0.dist-info/zip-safe,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
