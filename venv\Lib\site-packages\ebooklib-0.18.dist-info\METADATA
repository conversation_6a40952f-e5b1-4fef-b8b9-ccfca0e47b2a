Metadata-Version: 2.4
Name: EbookLib
Version: 0.18
Summary: Ebook library which can handle EPUB2/EPUB3 and Kindle format
Home-page: https://github.com/aer<PERSON><PERSON>/ebooklib
Author: <PERSON><PERSON><PERSON><PERSON>
Author-email: aer<PERSON><EMAIL>
License: GNU Affero General Public License
Keywords: ebook,epub,kindle
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Description-Content-Type: text/markdown
License-File: LICENSE.txt
License-File: AUTHORS.txt
Requires-Dist: lxml
Requires-Dist: six
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: requires-dist
Dynamic: summary

# About EbookLib

EbookLib is a Python library for managing EPUB2/EPUB3 and Kindle files. It's capable of reading and writing EPUB files programmatically (Kindle support is under development).

The API is designed to be as simple as possible, while at the same time making complex things possible too.  It has support for covers, table of contents, spine, guide, metadata and etc.

EbookLib is used in `Booktype <https://github.com/sourcefabric/Booktype/>`_ from Sourcefabric, as well as `sprits-it! <https://github.com/the-happy-hippo/sprits-it>`_, `fanfiction2ebook <https://github.com/ltouroumov/fanfiction2ebook>`_, `viserlalune <https://github.com/vjousse/viserlalune>`_ and `Telemeta <https://github.com/Parisson/Telemeta>`_.

Packages of EbookLib for GNU/Linux are available in `Debian <https://packages.debian.org/python-ebooklib>`_ and `Ubuntu <http://packages.ubuntu.com/python-ebooklib>`_. 

Sphinx documentation is generated from the templates in the docs/ directory and made available at http://ebooklib.readthedocs.io

# Usage

## Reading
```py
import ebooklib
from ebooklib import epub

book = epub.read_epub('test.epub')

for image in book.get_items_of_type(ebooklib.ITEM_IMAGE):
    print(image)
```


## Writing
```py
from ebooklib import epub

book = epub.EpubBook()

# set metadata
book.set_identifier("id123456")
book.set_title("Sample book")
book.set_language("en")

book.add_author("Author Authorowski")
book.add_author(
    "Danko Bananko",
    file_as="Gospodin Danko Bananko",
    role="ill",
    uid="coauthor",
)

# create chapter
c1 = epub.EpubHtml(title="Intro", file_name="chap_01.xhtml", lang="hr")
c1.content = (
    "<h1>Intro heading</h1>"
    "<p>Zaba je skocila u baru.</p>"
    '<p><img alt="[ebook logo]" src="static/ebooklib.gif"/><br/></p>'
)

# create image from the local image
image_content = open("ebooklib.gif", "rb").read()
img = epub.EpubImage(
    uid="image_1",
    file_name="static/ebooklib.gif",
    media_type="image/gif",
    content=image_content,
)

# add chapter
book.add_item(c1)
# add image
book.add_item(img)

# define Table Of Contents
book.toc = (
    epub.Link("chap_01.xhtml", "Introduction", "intro"),
    (epub.Section("Simple book"), (c1,)),
)

# add default NCX and Nav file
book.add_item(epub.EpubNcx())
book.add_item(epub.EpubNav())

# define CSS style
style = "BODY {color: white;}"
nav_css = epub.EpubItem(
    uid="style_nav",
    file_name="style/nav.css",
    media_type="text/css",
    content=style,
)

# add CSS file
book.add_item(nav_css)

# basic spine
book.spine = ["nav", c1]

# write to the file
epub.write_epub("test.epub", book, {})
```


# License
EbookLib is licensed under the `AGPL license <LICENSE.txt>`_.


# Authors
Full list of authors is in `AUTHORS.txt <AUTHORS.txt>`_ file.
