#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带日志记录版：同时运行20个static_real64.py进程
终端界面不显示子进程输出，但保存到日志文件
"""

import subprocess
import threading
import time
import os
from datetime import datetime

def run_single_process(process_id, log_dir):
    """运行单个static_real64.py进程，输出保存到日志文件"""
    command = "python static_real64.py --insecure --concurrent-streams 64 --headers-per-stream 180 https://cloudflare.linziyu.tech/"
    
    # 生成日志文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(log_dir, f"process_{process_id:02d}_{timestamp}.log")
    
    try:
        print(f"[进程 {process_id:2d}] 启动中... (日志: {log_file})")
        
        # 启动子进程，输出重定向到日志文件
        with open(log_file, 'w', encoding='utf-8') as f:
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=f,
                stderr=subprocess.STDOUT,  # 错误输出也重定向到同一文件
                cwd=os.getcwd()
            )
        
        print(f"[进程 {process_id:2d}] 已启动 (PID: {process.pid})")
        
        # 等待进程完成
        return_code = process.wait()
        
        if return_code == 0:
            print(f"[进程 {process_id:2d}] 完成 ✅")
        else:
            print(f"[进程 {process_id:2d}] 异常退出 (代码: {return_code}) ❌")
            
        return return_code
        
    except Exception as e:
        print(f"[进程 {process_id:2d}] 启动失败: {e} ❌")
        return -1

def main():
    num_processes = 20
    log_dir = "logs"
    
    # 创建日志目录
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        print(f"📁 创建日志目录: {os.path.abspath(log_dir)}")
    
    print(f"🚀 准备启动 {num_processes} 个并发进程...")
    print(f"📝 命令: python static_real64.py --insecure --concurrent-streams 64 --headers-per-stream 180 https://cloudflare.linziyu.tech/")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"📄 日志目录: {os.path.abspath(log_dir)}")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 80)
    
    start_time = time.time()
    threads = []
    
    # 启动所有进程
    for i in range(num_processes):
        process_id = i + 1
        
        # 创建线程运行子进程
        thread = threading.Thread(
            target=run_single_process,
            args=(process_id, log_dir),
            daemon=True
        )
        thread.start()
        threads.append(thread)
        
        # 稍微延迟启动，避免同时启动造成资源竞争
        time.sleep(0.1)
    
    print(f"✅ 所有 {num_processes} 个进程已启动")
    print("📊 等待所有进程完成...")
    print("-" * 80)
    
    try:
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        print(f"\n" + "=" * 80)
        print(f"🎉 所有进程执行完成!")
        print(f"⏱️  总耗时: {total_time:.2f} 秒")
        print(f"📁 日志文件保存在: {os.path.abspath(log_dir)}")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断执行")

if __name__ == "__main__":
    main()
