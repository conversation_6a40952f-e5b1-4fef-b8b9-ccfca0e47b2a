import multiprocessing
import subprocess
import sys
import os

# --- 配置 ---
# 请将这里替换为您要运行的Python脚本的绝对路径
TARGET_SCRIPT_PATH = r"D:\Work__\Pycharm_Work\qpack\aioquic\Static_test\static_real.py"

# 要同时运行的进程数量
NUMBER_OF_PROCESSES = 40


def run_target_script_silently():
    """
    此函数作为每个子进程的入口点。
    它使用 subprocess.Popen 启动目标脚本，并隐藏其命令行窗口。
    """
    # 检查目标脚本文件是否存在
    if not os.path.exists(TARGET_SCRIPT_PATH):
        # 在主进程中可能看不到这个打印，但这是一个好的实践
        print(f"错误：脚本文件未找到于 '{TARGET_SCRIPT_PATH}'")
        return

    try:
        # sys.executable 指的是当前运行此脚本的Python解释器（例如 C:\Python39\python.exe）
        # 我们用它来确保子进程使用相同的Python环境
        command = [sys.executable, TARGET_SCRIPT_PATH]

        # 在Windows上，CREATE_NO_WINDOW标志位可以阻止为新进程创建控制台窗口
        # 这是实现“不显示cmd界面”的关键
        CREATE_NO_WINDOW = 0x08000000

        # 启动子进程，但不等待它完成
        subprocess.Popen(command, creationflags=CREATE_NO_WINDOW)

    except Exception as e:
        print(f"启动脚本时发生错误: {e}")


if __name__ == "__main__":
    # 这行代码在Windows上使用multiprocessing时至关重要，
    # 它可以防止子进程无限递归地创建新的子进程。
    multiprocessing.freeze_support()

    print(f"准备启动 {NUMBER_OF_PROCESSES} 个并行进程...")
    print(f"目标脚本: {TARGET_SCRIPT_PATH}")

    # 创建一个列表来存放所有的进程对象
    processes = []

    # 循环创建并启动进程
    for i in range(NUMBER_OF_PROCESSES):
        # 创建一个新的进程，其任务是执行 run_target_script_silently 函数
        process = multiprocessing.Process(target=run_target_script_silently)
        processes.append(process)
        process.start()  # 启动进程
        print(f"  - 进程 {i+1}/{NUMBER_OF_PROCESSES} 已启动。")

    print("\n所有进程均已在后台启动。")
    print("主脚本执行完毕。")

    # --- 可选 ---
    # 如果您希望这个主脚本一直等待，直到所有20个子进程全部执行完毕再退出，
    # 请取消下面代码块的注释。
    # 否则，主脚本会在此处直接退出，但后台的20个进程会继续运行。
    #
    # print("\n正在等待所有子进程执行完毕...")
    # for process in processes:
    #     process.join()  # 等待该进程结束
    # print("所有子进程已执行完毕。")