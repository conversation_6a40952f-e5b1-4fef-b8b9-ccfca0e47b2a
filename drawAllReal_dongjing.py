import pandas as pd
import matplotlib.pyplot as plt
import matplotlib

# 有些系统可能需要明确指定后端才能弹出绘图窗口
# 如果您的环境不需要，可以注释掉下面这行
# matplotlib.use('TkAgg')


# --- 1. 配置与数据加载 ---

# *******************************************************************
# **重要**: 请将这里的路径修改为您 CSV 文件的实际路径
file_path = r'C:\Users\<USER>\Pictures\cdn_table\实际对比优化数据\国内外动静态源站80.csv' # 请确保这个路径是正确的
# *******************************************************************

# 使用 pandas 读取 CSV 文件
try:
    df = pd.read_csv(file_path)
except FileNotFoundError:
    print(f"Error: File not found at '{file_path}'. Please check the path.")
    exit()

# --- 2. 数据准备 (更新以包含新的CDN厂商) ---

# 清理列名中可能存在的多余空格
df.columns = df.columns.str.strip()

# 【更新】定义包含所有厂商的 send 和 receive 列名与标签
send_cols = {
    'ali_send(kbps)': 'Alibaba',
    'baidu_send(kbps)': 'Baidu',

    'cloudflare_send(kbps)': 'Cloudflare',
    'cloudfront_send(kbps)': 'CloudFront',
    'fastly_send(kbps)': 'Fastly',
        'ten_send(kbps)': 'Tencent'
}

receive_cols = {
    'ali_received(mbps)': 'Alibaba',
    'baidu_received(mbps)': 'Baidu',

    'cloudflare_received(mbps)': 'Cloudflare',
    'cloudfront_received(mbps)': 'CloudFront',
    'fastly_received(mbps)': 'Fastly',
        'ten_received(mbps)': 'Tencent',
}

# 【更新】根据您提供的颜色样式要求，定义新的颜色字典
colors = {
    'Alibaba': '#00468BFF',
    'Baidu': '#ED0000FF',

    'Cloudflare': '#F38020FF',  # 为Cloudflare选择一个醒目的颜色，例如橙色
    'CloudFront': '#42B540FF',
    'Fastly': '#0099B4FF',
    'Tencent': '#925E9FFF',
}

# 检查所有需要的列是否存在于CSV文件中
all_required_cols = list(send_cols.keys()) + list(receive_cols.keys())
missing_cols = [col for col in all_required_cols if col not in df.columns]

if missing_cols:
    print(f"Error: The following required columns were not found in the CSV file: {missing_cols}")
    print(f"Available columns are: {df.columns.tolist()}")
    exit()

# 时间戳处理
df['timestamp'] = pd.to_datetime(df['timestamp'], format='%H:%M:%S')
df['time_s'] = (df['timestamp'] - df['timestamp'].iloc[0]).dt.total_seconds()


# --- 3. 绘制并保存第一张图 (发送端) ---

print("正在生成发送端带宽消耗图...")
plt.figure(figsize=(12, 8)) # 可以适当加宽图像以容纳更多图例
ax1 = plt.gca()

# 按照您想要的图例顺序进行绘制
plot_order = ['Alibaba', 'Baidu', 'Cloudflare', 'CloudFront', 'Fastly', 'Tencent']
send_cols_reversed = {v: k for k, v in send_cols.items()} # 方便通过标签查找列名

for label in plot_order:
    if label in send_cols_reversed:
        col = send_cols_reversed[label]
        ax1.plot(df['time_s'], df[col], marker='', linestyle='-', linewidth=1.5, color=colors[label], label=label)

ax1.set_title('(a) Sender Bandwidth Consumption', fontsize=20, y=-0.15)
ax1.set_xlabel('Time (s)', fontsize=20)
ax1.set_ylabel('Sender Bandwidth (Kbps)', fontsize=20)
ax1.tick_params(axis='both', which='major', labelsize=18)
ax1.grid(True, linestyle='--', alpha=0.6)

legend1 = ax1.legend(loc='upper right', fontsize=12)
legend1.get_frame().set_alpha(1)
legend1.get_frame().set_facecolor('white')

plt.tight_layout()
plt.savefig('sender_bandwidth.pdf', format='pdf', bbox_inches='tight')
plt.show()


# --- 4. 绘制并保存第二张图 (接收端) ---

print("正在生成接收端带宽消耗图...")
plt.figure(figsize=(12, 8)) # 同样可以加宽
ax2 = plt.gca()

receive_cols_reversed = {v: k for k, v in receive_cols.items()} # 方便通过标签查找列名

for label in plot_order:
    if label in receive_cols_reversed:
        col = receive_cols_reversed[label]
        ax2.plot(df['time_s'], df[col], marker='', linestyle='-', linewidth=1.5, color=colors[label], label=label)


ax2.set_title('(b) Receiver Bandwidth Consumption', fontsize=20, y=-0.15)
ax2.set_xlabel('Time (s)', fontsize=20)
ax2.set_ylabel('Receiver Bandwidth (Mbps)', fontsize=20)
ax2.tick_params(axis='both', which='major', labelsize=18)
ax2.grid(True, linestyle='--', alpha=0.6)

legend2 = ax2.legend(loc='upper right', fontsize=12)
legend2.get_frame().set_alpha(1)
legend2.get_frame().set_facecolor('white')

plt.tight_layout()
plt.savefig('receiver_bandwidth.pdf', format='pdf', bbox_inches='tight')
plt.show()

print("所有图表已生成并保存完毕。")