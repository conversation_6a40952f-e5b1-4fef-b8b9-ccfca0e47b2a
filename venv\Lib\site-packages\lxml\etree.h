/* Generated by Cython 3.0.11 */

#ifndef __PYX_HAVE__lxml__etree
#define __PYX_HAVE__lxml__etree

#include "Python.h"
struct LxmlDocument;
struct LxmlElement;
struct LxmlElementTree;
struct LxmlElementTagMatcher;
struct LxmlElementIterator;
struct LxmlElementBase;
struct LxmlElementClassLookup;
struct LxmlFallbackElementClassLookup;

/* "lxml/etree.pyx":355
 * 
 * # type of a function that steps from node to node
 * ctypedef public xmlNode* (*_node_to_node_function)(xmlNode*)             # <<<<<<<<<<<<<<
 * 
 * 
 */
typedef xmlNode *(*_node_to_node_function)(xmlNode *);

/* "lxml/etree.pyx":371
 * @cython.final
 * @cython.freelist(8)
 * cdef public class _Document [ type LxmlDocumentType, object LxmlDocument ]:             # <<<<<<<<<<<<<<
 *     """Internal base class to reference a libxml document.
 * 
 */
struct LxmlDocument {
  PyObject_HEAD
  struct __pyx_vtabstruct_4lxml_5etree__Document *__pyx_vtab;
  int _ns_counter;
  PyObject *_prefix_tail;
  xmlDoc *_c_doc;
  struct __pyx_obj_4lxml_5etree__BaseParser *_parser;
};

/* "lxml/etree.pyx":720
 * 
 * @cython.no_gc_clear
 * cdef public class _Element [ type LxmlElementType, object LxmlElement ]:             # <<<<<<<<<<<<<<
 *     """Element class.
 * 
 */
struct LxmlElement {
  PyObject_HEAD
  struct LxmlDocument *_doc;
  xmlNode *_c_node;
  PyObject *_tag;
};

/* "lxml/etree.pyx":1894
 * 
 * 
 * cdef public class _ElementTree [ type LxmlElementTreeType,             # <<<<<<<<<<<<<<
 *                                  object LxmlElementTree ]:
 *     cdef _Document _doc
 */
struct LxmlElementTree {
  PyObject_HEAD
  struct __pyx_vtabstruct_4lxml_5etree__ElementTree *__pyx_vtab;
  struct LxmlDocument *_doc;
  struct LxmlElement *_context_node;
};

/* "lxml/etree.pyx":2668
 * 
 * 
 * cdef public class _ElementTagMatcher [ object LxmlElementTagMatcher,             # <<<<<<<<<<<<<<
 *                                        type LxmlElementTagMatcherType ]:
 *     """
 */
struct LxmlElementTagMatcher {
  PyObject_HEAD
  struct __pyx_vtabstruct_4lxml_5etree__ElementTagMatcher *__pyx_vtab;
  PyObject *_pystrings;
  int _node_type;
  char *_href;
  char *_name;
};

/* "lxml/etree.pyx":2699
 *                 self._name = NULL
 * 
 * cdef public class _ElementIterator(_ElementTagMatcher) [             # <<<<<<<<<<<<<<
 *     object LxmlElementIterator, type LxmlElementIteratorType ]:
 *     """
 */
struct LxmlElementIterator {
  struct LxmlElementTagMatcher __pyx_base;
  struct LxmlElement *_node;
  _node_to_node_function _next_element;
};

/* "src/lxml/classlookup.pxi":6
 * # Custom Element classes
 * 
 * cdef public class ElementBase(_Element) [ type LxmlElementBaseType,             # <<<<<<<<<<<<<<
 *                                           object LxmlElementBase ]:
 *     """ElementBase(*children, attrib=None, nsmap=None, **_extra)
 */
struct LxmlElementBase {
  struct LxmlElement __pyx_base;
};

/* "src/lxml/classlookup.pxi":210
 * # Element class lookup
 * 
 * ctypedef public object (*_element_class_lookup_function)(object, _Document, xmlNode*)             # <<<<<<<<<<<<<<
 * 
 * # class to store element class lookup functions
 */
typedef PyObject *(*_element_class_lookup_function)(PyObject *, struct LxmlDocument *, xmlNode *);

/* "src/lxml/classlookup.pxi":213
 * 
 * # class to store element class lookup functions
 * cdef public class ElementClassLookup [ type LxmlElementClassLookupType,             # <<<<<<<<<<<<<<
 *                                        object LxmlElementClassLookup ]:
 *     """ElementClassLookup(self)
 */
struct LxmlElementClassLookup {
  PyObject_HEAD
  _element_class_lookup_function _lookup_function;
};

/* "src/lxml/classlookup.pxi":221
 * 
 * 
 * cdef public class FallbackElementClassLookup(ElementClassLookup) \             # <<<<<<<<<<<<<<
 *          [ type LxmlFallbackElementClassLookupType,
 *            object LxmlFallbackElementClassLookup ]:
 */
struct LxmlFallbackElementClassLookup {
  struct LxmlElementClassLookup __pyx_base;
  struct __pyx_vtabstruct_4lxml_5etree_FallbackElementClassLookup *__pyx_vtab;
  struct LxmlElementClassLookup *fallback;
  _element_class_lookup_function _fallback_function;
};

#ifndef __PYX_HAVE_API__lxml__etree

#ifdef CYTHON_EXTERN_C
    #undef __PYX_EXTERN_C
    #define __PYX_EXTERN_C CYTHON_EXTERN_C
#elif defined(__PYX_EXTERN_C)
    #ifdef _MSC_VER
    #pragma message ("Please do not define the '__PYX_EXTERN_C' macro externally. Use 'CYTHON_EXTERN_C' instead.")
    #else
    #warning Please do not define the '__PYX_EXTERN_C' macro externally. Use 'CYTHON_EXTERN_C' instead.
    #endif
#else
  #ifdef __cplusplus
    #define __PYX_EXTERN_C extern "C"
  #else
    #define __PYX_EXTERN_C extern
  #endif
#endif

#ifndef DL_IMPORT
  #define DL_IMPORT(_T) _T
#endif

__PYX_EXTERN_C DL_IMPORT(PyTypeObject) LxmlDocumentType;
__PYX_EXTERN_C DL_IMPORT(PyTypeObject) LxmlElementType;
__PYX_EXTERN_C DL_IMPORT(PyTypeObject) LxmlElementTreeType;
__PYX_EXTERN_C DL_IMPORT(PyTypeObject) LxmlElementTagMatcherType;
__PYX_EXTERN_C DL_IMPORT(PyTypeObject) LxmlElementIteratorType;
__PYX_EXTERN_C DL_IMPORT(PyTypeObject) LxmlElementBaseType;
__PYX_EXTERN_C DL_IMPORT(PyTypeObject) LxmlElementClassLookupType;
__PYX_EXTERN_C DL_IMPORT(PyTypeObject) LxmlFallbackElementClassLookupType;

__PYX_EXTERN_C struct LxmlElement *deepcopyNodeToDocument(struct LxmlDocument *, xmlNode *);
__PYX_EXTERN_C struct LxmlElementTree *elementTreeFactory(struct LxmlElement *);
__PYX_EXTERN_C struct LxmlElementTree *newElementTree(struct LxmlElement *, PyObject *);
__PYX_EXTERN_C struct LxmlElementTree *adoptExternalDocument(xmlDoc *, PyObject *, int);
__PYX_EXTERN_C struct LxmlElement *elementFactory(struct LxmlDocument *, xmlNode *);
__PYX_EXTERN_C struct LxmlElement *makeElement(PyObject *, struct LxmlDocument *, PyObject *, PyObject *, PyObject *, PyObject *, PyObject *);
__PYX_EXTERN_C struct LxmlElement *makeSubElement(struct LxmlElement *, PyObject *, PyObject *, PyObject *, PyObject *, PyObject *);
__PYX_EXTERN_C void setElementClassLookupFunction(_element_class_lookup_function, PyObject *);
__PYX_EXTERN_C PyObject *lookupDefaultElementClass(PyObject *, PyObject *, xmlNode *);
__PYX_EXTERN_C PyObject *lookupNamespaceElementClass(PyObject *, PyObject *, xmlNode *);
__PYX_EXTERN_C PyObject *callLookupFallback(struct LxmlFallbackElementClassLookup *, struct LxmlDocument *, xmlNode *);
__PYX_EXTERN_C int tagMatches(xmlNode *, const xmlChar *, const xmlChar *);
__PYX_EXTERN_C struct LxmlDocument *documentOrRaise(PyObject *);
__PYX_EXTERN_C struct LxmlElement *rootNodeOrRaise(PyObject *);
__PYX_EXTERN_C int hasText(xmlNode *);
__PYX_EXTERN_C int hasTail(xmlNode *);
__PYX_EXTERN_C PyObject *textOf(xmlNode *);
__PYX_EXTERN_C PyObject *tailOf(xmlNode *);
__PYX_EXTERN_C int setNodeText(xmlNode *, PyObject *);
__PYX_EXTERN_C int setTailText(xmlNode *, PyObject *);
__PYX_EXTERN_C PyObject *attributeValue(xmlNode *, xmlAttr *);
__PYX_EXTERN_C PyObject *attributeValueFromNsName(xmlNode *, const xmlChar *, const xmlChar *);
__PYX_EXTERN_C PyObject *getAttributeValue(struct LxmlElement *, PyObject *, PyObject *);
__PYX_EXTERN_C PyObject *iterattributes(struct LxmlElement *, int);
__PYX_EXTERN_C PyObject *collectAttributes(xmlNode *, int);
__PYX_EXTERN_C int setAttributeValue(struct LxmlElement *, PyObject *, PyObject *);
__PYX_EXTERN_C int delAttribute(struct LxmlElement *, PyObject *);
__PYX_EXTERN_C int delAttributeFromNsName(xmlNode *, const xmlChar *, const xmlChar *);
__PYX_EXTERN_C int hasChild(xmlNode *);
__PYX_EXTERN_C xmlNode *findChild(xmlNode *, Py_ssize_t);
__PYX_EXTERN_C xmlNode *findChildForwards(xmlNode *, Py_ssize_t);
__PYX_EXTERN_C xmlNode *findChildBackwards(xmlNode *, Py_ssize_t);
__PYX_EXTERN_C xmlNode *nextElement(xmlNode *);
__PYX_EXTERN_C xmlNode *previousElement(xmlNode *);
__PYX_EXTERN_C void appendChild(struct LxmlElement *, struct LxmlElement *);
__PYX_EXTERN_C int appendChildToElement(struct LxmlElement *, struct LxmlElement *);
__PYX_EXTERN_C PyObject *pyunicode(const xmlChar *);
__PYX_EXTERN_C PyObject *utf8(PyObject *);
__PYX_EXTERN_C PyObject *getNsTag(PyObject *);
__PYX_EXTERN_C PyObject *getNsTagWithEmptyNs(PyObject *);
__PYX_EXTERN_C PyObject *namespacedName(xmlNode *);
__PYX_EXTERN_C PyObject *namespacedNameFromNsName(const xmlChar *, const xmlChar *);
__PYX_EXTERN_C void iteratorStoreNext(struct LxmlElementIterator *, struct LxmlElement *);
__PYX_EXTERN_C void initTagMatch(struct LxmlElementTagMatcher *, PyObject *);
__PYX_EXTERN_C xmlNs *findOrBuildNodeNsPrefix(struct LxmlDocument *, xmlNode *, const xmlChar *, const xmlChar *);

#endif /* !__PYX_HAVE_API__lxml__etree */

/* WARNING: the interface of the module init function changed in CPython 3.5. */
/* It now returns a PyModuleDef instance instead of a PyModule instance. */

#if PY_MAJOR_VERSION < 3
PyMODINIT_FUNC initetree(void);
#else
/* WARNING: Use PyImport_AppendInittab("etree", PyInit_etree) instead of calling PyInit_etree directly from Python 3.5 */
PyMODINIT_FUNC PyInit_etree(void);

#if PY_VERSION_HEX >= 0x03050000 && (defined(__GNUC__) || defined(__clang__) || defined(_MSC_VER) || (defined(__cplusplus) && __cplusplus >= 201402L))
#if defined(__cplusplus) && __cplusplus >= 201402L
[[deprecated("Use PyImport_AppendInittab(\"etree\", PyInit_etree) instead of calling PyInit_etree directly.")]] inline
#elif defined(__GNUC__) || defined(__clang__)
__attribute__ ((__deprecated__("Use PyImport_AppendInittab(\"etree\", PyInit_etree) instead of calling PyInit_etree directly."), __unused__)) __inline__
#elif defined(_MSC_VER)
__declspec(deprecated("Use PyImport_AppendInittab(\"etree\", PyInit_etree) instead of calling PyInit_etree directly.")) __inline
#endif
static PyObject* __PYX_WARN_IF_PyInit_etree_INIT_CALLED(PyObject* res) {
  return res;
}
#define PyInit_etree() __PYX_WARN_IF_PyInit_etree_INIT_CALLED(PyInit_etree())
#endif
#endif

#endif /* !__PYX_HAVE__lxml__etree */
