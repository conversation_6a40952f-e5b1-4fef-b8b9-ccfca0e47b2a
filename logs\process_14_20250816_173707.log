Traceback (most recent call last):
  File "D:\Work__\Pycharm_Work\qpack\static_real64.py", line 599, in <module>
    asyncio.run(
  File "D:\Work__\Python_Version\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "D:\Work__\Python_Version\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Work__\Python_Version\Lib\asyncio\base_events.py", line 654, in run_until_complete
    return future.result()
           ^^^^^^^^^^^^^^^
  File "D:\Work__\Pycharm_Work\qpack\static_real64.py", line 434, in run
    print(f"\U0001f3af ��̬��ͷ���������� v1.0")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3af' in position 0: illegal multibyte sequence
