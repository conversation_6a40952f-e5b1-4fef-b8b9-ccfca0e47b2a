from typing import Optional

class BufferReadError(ValueError): ...
class BufferWriteError(ValueError): ...

class Buffer:
    def __init__(self, capacity: Optional[int] = 0, data: Optional[bytes] = None): ...
    @property
    def capacity(self) -> int: ...
    @property
    def data(self) -> bytes: ...
    def data_slice(self, start: int, end: int) -> bytes: ...
    def eof(self) -> bool: ...
    def seek(self, pos: int) -> None: ...
    def tell(self) -> int: ...
    def pull_bytes(self, length: int) -> bytes: ...
    def pull_uint8(self) -> int: ...
    def pull_uint16(self) -> int: ...
    def pull_uint32(self) -> int: ...
    def pull_uint64(self) -> int: ...
    def pull_uint_var(self) -> int: ...
    def push_bytes(self, value: bytes) -> None: ...
    def push_uint8(self, value: int) -> None: ...
    def push_uint16(self, value: int) -> None: ...
    def push_uint32(self, v: int) -> None: ...
    def push_uint64(self, v: int) -> None: ...
    def push_uint_var(self, value: int) -> None: ...
