#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多进程并发运行static_real64.py脚本
同时启动多个子进程，终端界面不显示输出
"""

import subprocess
import threading
import time
import os
import sys
from datetime import datetime
import argparse
import signal
import json

class MultiProcessRunner:
    def __init__(self, num_processes=20, log_to_file=True):
        self.num_processes = num_processes
        self.log_to_file = log_to_file
        self.processes = []
        self.threads = []
        self.running = True
        self.start_time = None
        self.log_dir = "logs"
        
        # 创建日志目录
        if self.log_to_file and not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def run_single_process(self, process_id, command, log_file=None):
        """运行单个子进程"""
        try:
            print(f"[进程 {process_id:2d}] 启动中...")
            
            # 配置子进程参数
            if log_file:
                # 输出重定向到文件
                with open(log_file, 'w', encoding='utf-8') as f:
                    process = subprocess.Popen(
                        command,
                        shell=True,
                        stdout=f,
                        stderr=subprocess.STDOUT,
                        cwd=os.getcwd(),
                        creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                    )
            else:
                # 完全静默运行
                process = subprocess.Popen(
                    command,
                    shell=True,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    cwd=os.getcwd(),
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
            
            self.processes.append(process)
            print(f"[进程 {process_id:2d}] 已启动 (PID: {process.pid})")
            
            # 等待进程完成
            return_code = process.wait()
            
            if return_code == 0:
                print(f"[进程 {process_id:2d}] 完成 ✅")
            else:
                print(f"[进程 {process_id:2d}] 异常退出 (代码: {return_code}) ❌")
                
            return return_code
            
        except Exception as e:
            print(f"[进程 {process_id:2d}] 启动失败: {e} ❌")
            return -1
    
    def start_all_processes(self, base_command):
        """启动所有进程"""
        print(f"🚀 准备启动 {self.num_processes} 个并发进程...")
        print(f"📝 基础命令: {base_command}")
        print(f"📁 工作目录: {os.getcwd()}")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 60)
        
        self.start_time = time.time()
        
        # 启动所有进程
        for i in range(self.num_processes):
            process_id = i + 1
            
            # 生成日志文件名
            if self.log_to_file:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                log_file = os.path.join(self.log_dir, f"process_{process_id:02d}_{timestamp}.log")
            else:
                log_file = None
            
            # 创建线程运行子进程
            thread = threading.Thread(
                target=self.run_single_process,
                args=(process_id, base_command, log_file),
                daemon=True
            )
            thread.start()
            self.threads.append(thread)
            
            # 稍微延迟启动，避免同时启动造成资源竞争
            time.sleep(0.1)
        
        print(f"✅ 所有 {self.num_processes} 个进程已启动")
        print("📊 实时状态监控中...")
        print("-" * 60)
    
    def monitor_processes(self):
        """监控进程状态"""
        try:
            while self.running and any(thread.is_alive() for thread in self.threads):
                # 统计进程状态
                running_count = sum(1 for p in self.processes if p.poll() is None)
                completed_count = len(self.processes) - running_count
                
                elapsed_time = time.time() - self.start_time if self.start_time else 0
                
                print(f"\r⏱️  运行时间: {elapsed_time:.1f}s | "
                      f"🏃 运行中: {running_count} | "
                      f"✅ 已完成: {completed_count} | "
                      f"📊 总计: {len(self.processes)}", end="", flush=True)
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print(f"\n⚠️  收到中断信号，正在停止所有进程...")
            self.stop_all_processes()
    
    def stop_all_processes(self):
        """停止所有进程"""
        self.running = False
        
        print(f"\n🛑 正在终止 {len(self.processes)} 个进程...")
        
        for i, process in enumerate(self.processes):
            if process.poll() is None:  # 进程仍在运行
                try:
                    process.terminate()
                    print(f"[进程 {i+1:2d}] 已发送终止信号")
                except Exception as e:
                    print(f"[进程 {i+1:2d}] 终止失败: {e}")
        
        # 等待进程结束
        time.sleep(2)
        
        # 强制杀死仍在运行的进程
        for i, process in enumerate(self.processes):
            if process.poll() is None:
                try:
                    process.kill()
                    print(f"[进程 {i+1:2d}] 已强制终止")
                except Exception as e:
                    print(f"[进程 {i+1:2d}] 强制终止失败: {e}")
    
    def wait_for_completion(self):
        """等待所有进程完成"""
        try:
            # 等待所有线程完成
            for thread in self.threads:
                thread.join()
            
            # 统计最终结果
            total_time = time.time() - self.start_time if self.start_time else 0
            success_count = sum(1 for p in self.processes if p.returncode == 0)
            failed_count = len(self.processes) - success_count
            
            print(f"\n" + "=" * 60)
            print(f"🎉 所有进程执行完成!")
            print(f"⏱️  总耗时: {total_time:.2f} 秒")
            print(f"✅ 成功: {success_count} 个")
            print(f"❌ 失败: {failed_count} 个")
            print(f"📊 成功率: {(success_count/len(self.processes)*100):.1f}%")
            
            if self.log_to_file:
                print(f"📁 日志文件保存在: {os.path.abspath(self.log_dir)}")
            
            print("=" * 60)
            
        except KeyboardInterrupt:
            print(f"\n⚠️  用户中断执行")
            self.stop_all_processes()


def main():
    parser = argparse.ArgumentParser(description="多进程并发运行static_real64.py脚本")
    
    parser.add_argument(
        "--processes", "-p", type=int, default=20,
        help="并发进程数量 (默认: 20)"
    )
    parser.add_argument(
        "--concurrent-streams", type=int, default=64,
        help="每个进程的并发流数量 (默认: 64)"
    )
    parser.add_argument(
        "--headers-per-stream", type=int, default=180,
        help="每个流的头部数量 (默认: 180)"
    )
    parser.add_argument(
        "--header-type", type=str, default="strict-transport-security",
        help="静态头部类型 (默认: strict-transport-security)"
    )
    parser.add_argument(
        "--target-url", type=str, default="https://cloudflare.linziyu.tech/",
        help="目标URL (默认: https://cloudflare.linziyu.tech/)"
    )
    parser.add_argument(
        "--no-logs", action="store_true",
        help="不保存日志文件，完全静默运行"
    )
    parser.add_argument(
        "--script-path", type=str, default="static_real64.py",
        help="static_real64.py脚本路径 (默认: static_real64.py)"
    )
    
    args = parser.parse_args()
    
    # 构建基础命令
    base_command = (
        f"python {args.script_path} --insecure "
        f"--concurrent-streams {args.concurrent_streams} "
        f"--headers-per-stream {args.headers_per_stream} "
        f"--header-type {args.header_type} "
        f"{args.target_url}"
    )
    
    # 创建多进程运行器
    runner = MultiProcessRunner(
        num_processes=args.processes,
        log_to_file=not args.no_logs
    )
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print(f"\n⚠️  收到信号 {signum}，正在停止...")
        runner.stop_all_processes()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动所有进程
        runner.start_all_processes(base_command)
        
        # 监控进程状态
        runner.monitor_processes()
        
        # 等待完成
        runner.wait_for_completion()
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断")
        runner.stop_all_processes()
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        runner.stop_all_processes()


if __name__ == "__main__":
    main()
